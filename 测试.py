# main_serial_test.py
import sys
import threading
import time
from PyQt6.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QLineEdit, QMessageBox, QComboBox)
from PyQt6.QtCore import pyqtSlot, QThread, pyqtSignal

# 从 Communicator.py 导入我们定义的接口和实现
from Communicator import IControllerCommunicator, SerialCommunicator, TcpCommunicator, DataReaderThread 
# 从 HDControll.py 导入 dwrite 函数
from HDControll import dwrite

class SerialTestWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("串口指令测试工具 🚀")
        self.setGeometry(200, 200, 400, 300)

        self.communicator: IControllerCommunicator = None # 将在连接时实例化
        self.data_reader_thread: DataReaderThread = None

        self._init_ui()
        self._init_connections()

    def _init_ui(self):
        main_layout = QVBoxLayout()

        # --- 串口配置和连接 ---
        conn_layout = QHBoxLayout()
        self.resource_label = QLabel("串口资源名:")
        self.resource_input = QLineEdit("USB0::0xFFFF::0xFFFB::2073325E4232::RAW") # 默认值，根据实际情况修改
        conn_layout.addWidget(self.resource_label)
        conn_layout.addWidget(self.resource_input)

        self.connect_button = QPushButton("连接串口 ✅")
        self.disconnect_button = QPushButton("断开串口 ❌")
        self.disconnect_button.setEnabled(False) # 初始禁用断开按钮
        conn_layout.addWidget(self.connect_button)
        conn_layout.addWidget(self.disconnect_button)
        main_layout.addLayout(conn_layout)

        # --- 指令输入 ---
        cmd_input_layout = QHBoxLayout()
        self.item_label = QLabel("Item (0-255):")
        self.item_input = QLineEdit("1") # 默认 item
        self.item_input.setValidator(self.create_int_validator(0, 255))

        self.value_label = QLabel("Value (0-4294967295):")
        self.value_input = QLineEdit("1") # 默认 value
        self.value_input.setValidator(self.create_int_validator(0, 2147483647))

        self.send_button = QPushButton("发送指令 📤")
        self.send_button.setEnabled(False) # 初始禁用发送按钮

        cmd_input_layout.addWidget(self.item_label)
        cmd_input_layout.addWidget(self.item_input)
        cmd_input_layout.addWidget(self.value_label)
        cmd_input_layout.addWidget(self.value_input)
        cmd_input_layout.addWidget(self.send_button)
        main_layout.addLayout(cmd_input_layout)

        # --- 状态显示 ---
        self.status_label = QLabel("状态: 未连接。")
        main_layout.addWidget(self.status_label)

        self.setLayout(main_layout)

    def _init_connections(self):
        self.connect_button.clicked.connect(self.connect_serial)
        self.disconnect_button.clicked.connect(self.disconnect_serial)
        self.send_button.clicked.connect(self.send_command)

    def create_int_validator(self, min_val, max_val):
        from PyQt6.QtGui import QIntValidator
        validator = QIntValidator(min_val, max_val, self)
        return validator

    @pyqtSlot()
    def connect_serial(self):
        resource_name = self.resource_input.text()
        
        # 实例化 Communicator
        self.communicator = SerialCommunicator() # 使用 SerialCommunicator

        if self.communicator.connect({'resource_name': resource_name, 'timeout_ms': 1000}):
            self.status_label.setText(f"状态: 已连接到 {resource_name} ✅")
            self.connect_button.setEnabled(False)
            self.disconnect_button.setEnabled(True)
            self.send_button.setEnabled(True)

            # 启动数据读取线程，虽然本示例主要测试发送，但保持线程结构
            self.data_reader_thread = DataReaderThread(self.communicator)
            self.data_reader_thread.signals.error.connect(self.handle_thread_error)
            self.data_reader_thread.signals.data_ready.connect(self.handle_incoming_data) # 可选：处理接收数据
            self.data_reader_thread.start()

        else:
            self.status_label.setText("状态: 连接失败 ❌")
            QMessageBox.critical(self, "连接错误", f"无法连接到串口: {resource_name}")

    @pyqtSlot()
    def disconnect_serial(self):
        if self.data_reader_thread:
            self.data_reader_thread.stop()
            self.data_reader_thread = None

        if self.communicator:
            self.communicator.disconnect()
            self.communicator = None # 清空实例
        
        self.status_label.setText("状态: 已断开连接 😴")
        self.connect_button.setEnabled(True)
        self.disconnect_button.setEnabled(False)
        self.send_button.setEnabled(False)

    @pyqtSlot()
    def send_command(self):
        if not self.communicator or not self.communicator.is_connected():
            self.status_label.setText("状态: 未连接，无法发送指令 ⚠️")
            QMessageBox.warning(self, "发送错误", "串口未连接，请先连接串口。")
            return

        try:
            item = int(self.item_input.text())
            value = int(self.value_input.text())

            # 调用 dwrite 函数发送指令
            success = dwrite(self.communicator, item, value)

            if success:
                self.status_label.setText(f"状态: 指令 (Item: {item}, Value: {value}) 发送成功！🎉")
            else:
                self.status_label.setText(f"状态: 指令发送失败 😔 (Item: {item}, Value: {value})")
                QMessageBox.warning(self, "发送失败", "指令发送失败，请检查控制台输出。")

        except ValueError:
            self.status_label.setText("状态: 输入无效，请检查 Item 和 Value 是否为数字 🚫")
            QMessageBox.warning(self, "输入错误", "Item 和 Value 必须是有效的数字。")
        except Exception as e:
            self.status_label.setText(f"状态: 发送指令时发生错误: {str(e)[:30]} 🐛")
            QMessageBox.critical(self, "发送错误", f"发送指令时发生意外错误: {str(e)}")

    @pyqtSlot(str)
    def handle_thread_error(self, error_msg):
        self.status_label.setText(f"状态: 读取线程错误: {error_msg[:30]} ❗")
        QMessageBox.critical(self, "线程错误", f"数据读取线程发生错误: {error_msg}")
        self.disconnect_serial() # 错误时尝试断开连接

    @pyqtSlot(object)
    def handle_incoming_data(self, processed_data):
        # 这是一个可选的回调，如果你需要处理接收到的数据
        # print(f"DEBUG: 主线程收到处理后的数据: {processed_data}")
        # self.status_label.setText(f"状态: 收到数据 (长度: {len(processed_data[0] if processed_data else [])})")
        pass

    def closeEvent(self, event):
        """确保窗口关闭时线程也停止"""
        self.disconnect_serial()
        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = SerialTestWindow()
    window.show()
    sys.exit(app.exec())