('C:\\Users\\<USER>\\Desktop\\项目\\python复现评估\\python重写\\串口读取 '
 'v1.4\\测试程序\\build\\网口通信测试工具\\网口通信测试工具.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('O', None, 'OPTION'),
  ('O', None, 'OPTION'),
  ('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Desktop\\项目\\python复现评估\\python重写\\串口读取 '
   'v1.4\\测试程序\\build\\网口通信测试工具\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Desktop\\项目\\python复现评估\\python重写\\串口读取 '
   'v1.4\\测试程序\\build\\网口通信测试工具\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Desktop\\项目\\python复现评估\\python重写\\串口读取 '
   'v1.4\\测试程序\\build\\网口通信测试工具\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Desktop\\项目\\python复现评估\\python重写\\串口读取 '
   'v1.4\\测试程序\\build\\网口通信测试工具\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Desktop\\项目\\python复现评估\\python重写\\串口读取 '
   'v1.4\\测试程序\\build\\网口通信测试工具\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Desktop\\项目\\python复现评估\\python重写\\串口读取 '
   'v1.4\\测试程序\\build\\网口通信测试工具\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('网口通信测试',
   'C:\\Users\\<USER>\\Desktop\\项目\\python复现评估\\python重写\\串口读取 '
   'v1.4\\测试程序\\网口通信测试.py',
   'PYSOURCE-2'),
  ('python312.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\python312.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qdirect2d.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\platforms\\qdirect2d.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('_decimal.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\DLLs\\select.pyd',
   'EXTENSION'),
  ('PyQt6\\QtCore.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyQt6\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt6\\sip.cp312-win_amd64.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyQt6\\sip.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtWidgets.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyQt6\\QtWidgets.pyd',
   'EXTENSION'),
  ('PyQt6\\QtGui.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyQt6\\QtGui.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\zlib.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('Qt6Widgets.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\Qt6Widgets.dll',
   'BINARY'),
  ('Qt6Core.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\Qt6Core.dll',
   'BINARY'),
  ('Qt6Gui.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\Qt6Gui.dll',
   'BINARY'),
  ('MSVCP140.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\MSVCP140.dll',
   'BINARY'),
  ('Qt6Svg.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\Qt6Svg.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libjpeg.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\libjpeg.dll',
   'BINARY'),
  ('Qt6Network.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\Qt6Network.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('liblzma.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\LIBBZ2.dll',
   'BINARY'),
  ('python3.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\python3.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('MSVCP140_2.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\MSVCP140_2.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\ucrtbase.dll',
   'BINARY'),
  ('MSVCP140_1.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('icuuc73.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\icuuc73.dll',
   'BINARY'),
  ('zstd.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\zstd.dll',
   'BINARY'),
  ('icuin73.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\icuin73.dll',
   'BINARY'),
  ('libpng16.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\libpng16.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('icudt73.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\icudt73.dll',
   'BINARY'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\项目\\python复现评估\\python重写\\串口读取 '
   'v1.4\\测试程序\\build\\网口通信测试工具\\base_library.zip',
   'DATA')],
 'python312.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
