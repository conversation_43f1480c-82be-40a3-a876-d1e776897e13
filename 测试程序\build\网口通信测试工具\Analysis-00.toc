(['C:\\Users\\<USER>\\Desktop\\项目\\python复现评估\\python重写\\串口读取 '
  'v1.4\\测试程序\\网口通信测试.py'],
 ['C:\\Users\\<USER>\\Desktop\\项目\\python复现评估\\python重写\\串口读取 v1.4\\测试程序'],
 [],
 [('D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 2,
 [],
 [],
 '3.12.11 | packaged by Anaconda, Inc. | (main, Jun  5 2025, 12:58:53) [MSC '
 'v.1929 64 bit (AMD64)]',
 [('pyi_rth_inspect',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt6',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt6.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('网口通信测试',
   'C:\\Users\\<USER>\\Desktop\\项目\\python复现评估\\python重写\\串口读取 '
   'v1.4\\测试程序\\网口通信测试.py',
   'PYSOURCE-2')],
 [('pkgutil',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\pkgutil.py',
   'PYMODULE-2'),
  ('zipimport',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\zipimport.py',
   'PYMODULE-2'),
  ('importlib.readers',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\readers.py',
   'PYMODULE-2'),
  ('importlib.resources.readers',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE-2'),
  ('importlib.resources._itertools',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE-2'),
  ('importlib.resources.abc',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE-2'),
  ('typing',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\typing.py',
   'PYMODULE-2'),
  ('contextlib',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\contextlib.py',
   'PYMODULE-2'),
  ('importlib.resources',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE-2'),
  ('importlib.resources._legacy',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE-2'),
  ('importlib.resources._common',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE-2'),
  ('importlib.resources._adapters',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE-2'),
  ('tempfile',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\tempfile.py',
   'PYMODULE-2'),
  ('random',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\random.py',
   'PYMODULE-2'),
  ('statistics',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\statistics.py',
   'PYMODULE-2'),
  ('decimal',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\decimal.py',
   'PYMODULE-2'),
  ('_pydecimal',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\_pydecimal.py',
   'PYMODULE-2'),
  ('contextvars',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\contextvars.py',
   'PYMODULE-2'),
  ('fractions',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\fractions.py',
   'PYMODULE-2'),
  ('numbers',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\numbers.py',
   'PYMODULE-2'),
  ('hashlib',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\hashlib.py',
   'PYMODULE-2'),
  ('logging',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\logging\\__init__.py',
   'PYMODULE-2'),
  ('pickle',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\pickle.py',
   'PYMODULE-2'),
  ('pprint',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\pprint.py',
   'PYMODULE-2'),
  ('dataclasses',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\dataclasses.py',
   'PYMODULE-2'),
  ('copy',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\copy.py',
   'PYMODULE-2'),
  ('_compat_pickle',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\_compat_pickle.py',
   'PYMODULE-2'),
  ('string',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\string.py',
   'PYMODULE-2'),
  ('bisect',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\bisect.py',
   'PYMODULE-2'),
  ('shutil',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\shutil.py',
   'PYMODULE-2'),
  ('tarfile',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\tarfile.py',
   'PYMODULE-2'),
  ('argparse',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\argparse.py',
   'PYMODULE-2'),
  ('textwrap',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\textwrap.py',
   'PYMODULE-2'),
  ('gettext',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\gettext.py',
   'PYMODULE-2'),
  ('gzip',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\gzip.py',
   'PYMODULE-2'),
  ('_compression',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\_compression.py',
   'PYMODULE-2'),
  ('lzma',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\lzma.py',
   'PYMODULE-2'),
  ('bz2',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\bz2.py',
   'PYMODULE-2'),
  ('fnmatch',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\fnmatch.py',
   'PYMODULE-2'),
  ('zipfile',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\zipfile\\__init__.py',
   'PYMODULE-2'),
  ('zipfile._path',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE-2'),
  ('zipfile._path.glob',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE-2'),
  ('py_compile',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\py_compile.py',
   'PYMODULE-2'),
  ('importlib._bootstrap_external',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE-2'),
  ('importlib.metadata',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE-2'),
  ('importlib.abc',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\abc.py',
   'PYMODULE-2'),
  ('importlib._abc',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\_abc.py',
   'PYMODULE-2'),
  ('importlib._bootstrap',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE-2'),
  ('importlib.metadata._itertools',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE-2'),
  ('importlib.metadata._functools',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE-2'),
  ('importlib.metadata._collections',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE-2'),
  ('importlib.metadata._meta',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE-2'),
  ('importlib.metadata._adapters',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE-2'),
  ('importlib.metadata._text',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE-2'),
  ('email.message',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\message.py',
   'PYMODULE-2'),
  ('email.policy',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\policy.py',
   'PYMODULE-2'),
  ('email.contentmanager',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\contentmanager.py',
   'PYMODULE-2'),
  ('email.quoprimime',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\quoprimime.py',
   'PYMODULE-2'),
  ('email.headerregistry',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\headerregistry.py',
   'PYMODULE-2'),
  ('email._header_value_parser',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\_header_value_parser.py',
   'PYMODULE-2'),
  ('urllib',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\urllib\\__init__.py',
   'PYMODULE-2'),
  ('email.iterators',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\iterators.py',
   'PYMODULE-2'),
  ('email.generator',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\generator.py',
   'PYMODULE-2'),
  ('email._encoded_words',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\_encoded_words.py',
   'PYMODULE-2'),
  ('base64',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\base64.py',
   'PYMODULE-2'),
  ('getopt',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\getopt.py',
   'PYMODULE-2'),
  ('email.charset',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\charset.py',
   'PYMODULE-2'),
  ('email.encoders',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\encoders.py',
   'PYMODULE-2'),
  ('email.base64mime',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\base64mime.py',
   'PYMODULE-2'),
  ('email._policybase',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\_policybase.py',
   'PYMODULE-2'),
  ('email.header',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\header.py',
   'PYMODULE-2'),
  ('email.errors',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\errors.py',
   'PYMODULE-2'),
  ('email.utils',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\utils.py',
   'PYMODULE-2'),
  ('email._parseaddr',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\_parseaddr.py',
   'PYMODULE-2'),
  ('calendar',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\calendar.py',
   'PYMODULE-2'),
  ('urllib.parse',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\urllib\\parse.py',
   'PYMODULE-2'),
  ('ipaddress',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\ipaddress.py',
   'PYMODULE-2'),
  ('quopri',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\quopri.py',
   'PYMODULE-2'),
  ('email',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\__init__.py',
   'PYMODULE-2'),
  ('email.parser',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\parser.py',
   'PYMODULE-2'),
  ('email.feedparser',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\feedparser.py',
   'PYMODULE-2'),
  ('csv',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\csv.py',
   'PYMODULE-2'),
  ('tokenize',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\tokenize.py',
   'PYMODULE-2'),
  ('token',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\token.py',
   'PYMODULE-2'),
  ('pathlib',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\pathlib.py',
   'PYMODULE-2'),
  ('inspect',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\inspect.py',
   'PYMODULE-2'),
  ('dis',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\dis.py',
   'PYMODULE-2'),
  ('opcode',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\opcode.py',
   'PYMODULE-2'),
  ('ast',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\ast.py',
   'PYMODULE-2'),
  ('importlib.machinery',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\machinery.py',
   'PYMODULE-2'),
  ('importlib.util',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\util.py',
   'PYMODULE-2'),
  ('importlib',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\__init__.py',
   'PYMODULE-2'),
  ('_pyi_rth_utils.qt',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE-2'),
  ('_pyi_rth_utils',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE-2'),
  ('_py_abc',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\_py_abc.py',
   'PYMODULE-2'),
  ('stringprep',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\stringprep.py',
   'PYMODULE-2'),
  ('tracemalloc',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\tracemalloc.py',
   'PYMODULE-2'),
  ('datetime',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\datetime.py',
   'PYMODULE-2'),
  ('_pydatetime',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\_pydatetime.py',
   'PYMODULE-2'),
  ('_strptime',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\_strptime.py',
   'PYMODULE-2'),
  ('subprocess',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\subprocess.py',
   'PYMODULE-2'),
  ('selectors',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\selectors.py',
   'PYMODULE-2'),
  ('signal',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\signal.py',
   'PYMODULE-2'),
  ('PyQt6',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE-2'),
  ('threading',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\threading.py',
   'PYMODULE-2'),
  ('_threading_local',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\_threading_local.py',
   'PYMODULE-2'),
  ('struct',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\struct.py',
   'PYMODULE-2'),
  ('socket',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\socket.py',
   'PYMODULE-2')],
 [('python312.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\python312.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qico.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qsvg.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qdirect2d.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\platforms\\qdirect2d.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qwindows.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qjpeg.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qoffscreen.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\platforms\\qminimal.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt6\\Qt6\\plugins\\imageformats\\qgif.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\lib\\qt6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('_decimal.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\DLLs\\select.pyd',
   'EXTENSION'),
  ('PyQt6\\QtCore.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyQt6\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt6\\sip.cp312-win_amd64.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyQt6\\sip.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt6\\QtWidgets.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyQt6\\QtWidgets.pyd',
   'EXTENSION'),
  ('PyQt6\\QtGui.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyQt6\\QtGui.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\zlib.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('Qt6Widgets.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\Qt6Widgets.dll',
   'BINARY'),
  ('Qt6Core.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\Qt6Core.dll',
   'BINARY'),
  ('Qt6Gui.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\Qt6Gui.dll',
   'BINARY'),
  ('MSVCP140.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\MSVCP140.dll',
   'BINARY'),
  ('Qt6Svg.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\Qt6Svg.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libjpeg.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\libjpeg.dll',
   'BINARY'),
  ('Qt6Network.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\Qt6Network.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('liblzma.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\LIBBZ2.dll',
   'BINARY'),
  ('python3.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\python3.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('MSVCP140_2.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\MSVCP140_2.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\ucrtbase.dll',
   'BINARY'),
  ('MSVCP140_1.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('icuuc73.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\icuuc73.dll',
   'BINARY'),
  ('zstd.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\zstd.dll',
   'BINARY'),
  ('icuin73.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\icuin73.dll',
   'BINARY'),
  ('libpng16.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\libpng16.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('icudt73.dll',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Library\\bin\\icudt73.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'C:\\Users\\<USER>\\Desktop\\项目\\python复现评估\\python重写\\串口读取 '
   'v1.4\\测试程序\\build\\网口通信测试工具\\base_library.zip',
   'DATA')],
 [('reprlib',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\reprlib.py',
   'PYMODULE'),
  ('_collections_abc',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('genericpath',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\genericpath.py',
   'PYMODULE'),
  ('_weakrefset',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\_weakrefset.py',
   'PYMODULE'),
  ('abc',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\abc.py',
   'PYMODULE'),
  ('linecache',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\linecache.py',
   'PYMODULE'),
  ('collections.abc',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('io',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\io.py',
   'PYMODULE'),
  ('sre_compile',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\sre_compile.py',
   'PYMODULE'),
  ('re._parser',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\re\\_parser.py',
   'PYMODULE'),
  ('re._constants',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\re\\_casefix.py',
   'PYMODULE'),
  ('re',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\re\\__init__.py',
   'PYMODULE'),
  ('ntpath',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\ntpath.py',
   'PYMODULE'),
  ('enum',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\enum.py',
   'PYMODULE'),
  ('codecs',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\codecs.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('locale',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\locale.py',
   'PYMODULE'),
  ('keyword',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\keyword.py',
   'PYMODULE'),
  ('stat',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\stat.py',
   'PYMODULE'),
  ('sre_constants',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('operator',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\operator.py',
   'PYMODULE'),
  ('copyreg',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\copyreg.py',
   'PYMODULE'),
  ('sre_parse',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\sre_parse.py',
   'PYMODULE'),
  ('posixpath',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\posixpath.py',
   'PYMODULE'),
  ('warnings',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\warnings.py',
   'PYMODULE'),
  ('functools',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\functools.py',
   'PYMODULE'),
  ('types',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\types.py',
   'PYMODULE'),
  ('traceback',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\traceback.py',
   'PYMODULE'),
  ('heapq',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\heapq.py',
   'PYMODULE'),
  ('weakref',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\weakref.py',
   'PYMODULE'),
  ('os',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\os.py',
   'PYMODULE')])
