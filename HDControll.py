from controllerutil import dwrite, AMP_State_to_value,AOitem,load_config_and_initialize
from enum import Enum
from Communicator import IControllerCommunicator
from settings import settings
from Communicator import SerialCommunicator, TcpCommunicator, DataReaderThread,VirtualCommunicator

# TODO: 这里缺少一些AOItem 导致无法正确设置对应寄存器
# 定义一个模块级别的通信器实例
_communicator: IControllerCommunicator = None

def set_controller_communicator(comm: IControllerCommunicator):
    """
    设置 HDControll 模块使用的通信器实例。
    前端或其他模块在初始化时应调用此函数。
    """
    global _communicator
    _communicator = comm
    print("HDControll 模块的通信器已设置。")

def _check_communicator():
    """内部辅助函数，检查通信器是否已设置"""
    if _communicator is None or not _communicator.is_connected():
        print("错误: 通信器未设置或未连接，无法发送命令！")
        return False
    return True

class AOMode(Enum):
    """
    AOMode枚举类
    """
    Zero = 0
    Hold = 1
    Constant = 2
    SingleScan = 3
    ContinuousScan = 4


def setAOMode(AOChannel,mode:AOMode):
    """
    设置AOMode
    """
    if mode == AOMode.Zero:
        dwrite(_communicator,AOitem((17,0),AOChannel)[0],0)
    elif mode == AOMode.Hold:
        dwrite(_communicator,AOitem((17,2),AOChannel)[0],2)
    elif mode == AOMode.Constant:
        dwrite(_communicator,AOitem((17,3),AOChannel)[0],3)
    elif mode == AOMode.SingleScan:
        dwrite(_communicator,AOitem((17,4),AOChannel)[0],4)
    elif mode == AOMode.ContinuousScan:
        dwrite(_communicator,AOitem((17,5),AOChannel)[0],5)
# --- 电机控制 ---
def Motor_Upward():
    """
    电机向上(反转)
    """
    dwrite(_communicator,1,2)
def Motor_Downward():
    """
    电机向下（正转）
    """
    dwrite(_communicator,1,1)


def Motor_Step():
    """
    电机步进
    """
    dwrite(_communicator,1,3)

def Motor_StepValue(value):
    """
    电机步进值
    """
    dwrite(_communicator,6,value)

def Motor_Stop():
    """
    电机停止
    """
    dwrite(_communicator,1,0)

def Motor_PWMPeriod(value):
    """
    电机PWM周期
    """
    dwrite(_communicator,2,value)
    
def Motor_PWMHighWidth(value):
    """
    电机PWM高宽
    """
    dwrite(_communicator,3,value)
# --- ADC ---
def ADC_Start():
    """
    ADC开始
    """
    dwrite(_communicator,4,1)
    
def ADC_Stop():
    """
    ADC停止
    """
    dwrite(_communicator,4,0)
    
def ADC_dT(value):
    """
    ADC时间
    """
    dwrite(_communicator,5,value)
# --- 加热器控制 ---
def Heater_Start():
    """
    加热器开始
    """
    dwrite(_communicator,10,1)
    
def Heater_Stop():
    """
    加热器停止
    """
    dwrite(_communicator,10,0)
    
def Heater_Target(value):
    """
    加热器目标
    """
    dwrite(_communicator,11,value*10)
# 放大器控制
def AMP_State(state):
    """
    放大器状态
    """
    switch0,switch1 = AMP_State_to_value(state)
    dwrite(_communicator,12,switch0)
    dwrite(_communicator,13,switch1)
# 常值输出
def Constant_Output(AOChannel,value):
    """
    常值输出设置
    Args:
        value: 输出电压
        AOChannel: 字符串，表示 AO 通道，可选值： "Bias", "Piezo", "RE", "CE".    
    """
    item = AOitem((18,0),AOChannel)
    dwrite(_communicator,item[0],value * 1e6)
# 扫描
def Scan_dT(AOChannel,value):
    """
    扫描时间
    """
    item = AOitem((19,0),AOChannel)
    dwrite(_communicator,item[0],value)
    
def Scan_Positive_dT(AOChannel,value):
    """
    正向扫描时间
    """
    item = AOitem((20,0),AOChannel)
    dwrite(_communicator,item[0],value)    

def Scan_Negative_dT(AOChannel,value):
    """
    反向扫描时间
    """
    item = AOitem((21,0),AOChannel)
    dwrite(_communicator,item[0],value)    
    
def Scan_Delay_Positive_Enable(AOChannel, value):
    """
    正向扫描延时使能
    参数:
        value: 0/1    
    """
    item = AOitem((24,0),AOChannel)
    dwrite(_communicator,item[0],value)   

def Scan_Delay_Negative_Enable(AOChannel, value):
    """
    反向扫描延时使能
    参数:
        value: 0/1    
    """
    item = AOitem((25,0),AOChannel)
    dwrite(_communicator,item[0],value)           

    
def Scan_Delay_Positive(AOChannel, value):
    """
    正向扫描延时
    参数:
        value: 0/1    
    """
    item = AOitem((26,0),AOChannel)
    dwrite(_communicator,item[0],value)   

def Scan_Delay_Negative(AOChannel, value):
    """
    反向扫描延时
    参数:
        value: 0/1    
    """
    item = AOitem((27,0),AOChannel)
    dwrite(_communicator,item[0],value)           
# 连续扫描
def ContinuousScan_UpperLimit(AOChannel, value):
    """
    连续扫描上限
    """
    item = AOitem((22,0),AOChannel)
    dwrite(_communicator,item[0],value * 1e6 )   

def ContinuousScan_LowerLimit(AOChannel, value):
    """
    连续扫描上限
    """
    item = AOitem((23,0),AOChannel)
    dwrite(_communicator,item[0],value * 1e6 )    
# 单次扫描
def SingleScan_Up(AOChannel):
    """
    单次向上扫描
    """
    item = AOitem((28,0),AOChannel)
    dwrite(_communicator, item[0],0)

def SingleScan_Down(AOChannel):
    """
    单次向下扫描
    """
    item = AOitem((28,1),AOChannel)
    dwrite(_communicator, item[0],1)


def ContinuousScan_UpperLimit(AOChannel, value):
    """
    单次向上扫描上限
    """
    item = AOitem((29,0),AOChannel)
    dwrite(_communicator,item[0],value * 1e6 )   

def ContinuousScan_LowerLimit(AOChannel, value):
    """
    单次向下扫描下限
    """
    item = AOitem((30,0),AOChannel)
    dwrite(_communicator,item[0],value * 1e6 )  
# 悬停(新版)
def VibrateType(AOChannel, value):
    """
    波形类型
    """
    item = AOitem((31,0),AOChannel)
    dwrite(_communicator,item[0],value)      

def Hover_Enable(value:bool):
    """
    悬停使能
    """
    dwrite(_communicator,1 if value else 0)      

def Hover_UpperLimit_Positive(value):
    """
    悬停正上限
    范围 [0,32767]
    """
    dwrite(_communicator,value)

def Hover_UpperLimit_Negative(value):
    """
    悬停正下限
    范围 [0,32767]
    """
    dwrite(_communicator,value)

def Hover_LowerLimit_Negative(value):
    """
    悬停负上限
    范围 [32768,0]
    """
    dwrite(_communicator,value)

def Hover_LowerLimit_Negative(value):
    """
    悬停负下限
    范围 [32768,0]
    """
    dwrite(_communicator,value) 

def Hover_Sensitive(value):
    """
    悬停灵敏度
    """
    dwrite(_communicator,value)

def initialize_controller(comm_type='Serial', resource_name=None, ip_address=None, port=None, timeout_ms=1000):
    """
    初始化控制器连接和相关线程
    
    Args:
        comm_type: 通信类型，'Serial' 或 'TCP'
        resource_name: 串口资源名称，仅当 comm_type 为 'Serial' 时使用
        ip_address: TCP IP地址，仅当 comm_type 为 'TCP' 时使用
        port: TCP 端口，仅当 comm_type 为 'TCP' 时使用
        timeout_ms: 超时时间(毫秒)
    
    Returns:
        tuple: (communicator, data_reader_thread, status_message)
            - communicator: 通信器实例
            - data_reader_thread: 数据读取线程实例
            - status_message: 状态信息
    """
    
    try:
        # 1. 根据设置选择并实例化通信器
        if comm_type == "Serial":
            communicator = SerialCommunicator()
            comm_config = {
                'resource_name': resource_name or "USB0::0xFFFF::0xFFFB::2073325E4232::RAW",
                'timeout_ms': timeout_ms
            }
        elif comm_type == "TCP":
            communicator = TcpCommunicator()
            comm_config = {
                'ip_address': ip_address or '************',
                'port': port or 5001,
                'timeout_ms': timeout_ms
            }
        elif comm_type == "Virtual":
            communicator = VirtualCommunicator()
            comm_config = {
                'tdms_file': 'recorded_data.txt'
            }
        else:
            return None, None, f"不支持的通信类型: {comm_type}"

        # 2. 连接控制器
        if not communicator.connect(comm_config):
            return None, None, "连接到控制器失败，请检查配置和设备。"
        
        status_message = f"已连接 ({comm_type})"
        print(f"控制器已连接 ({comm_type})")

        # 3. 设置 HDControll 模块的通信器
        set_controller_communicator(communicator)

        # 4. 初始化控制器（发送配置参数）
        print("开始初始化控制器...")
        if load_config_and_initialize(communicator): 
            print("控制器初始化命令发送成功。")
            # 设置放大器状态
            try:
                AMP_State('Logarithmic')
                print(f"设置放大器状态: Logarithmic")
            except (AttributeError, ValueError) as e:
                print(f"设置放大器状态失败: {e}")
            
            # 设置偏置模式
            try:
                setAOMode('Bias',AOMode.Constant)
                print(f"设置偏置模式: Constant")
            except Exception as e:
                print(f"设置偏置模式失败: {e}")
            
            # 设置偏置电压
            try:
                bias_voltage_value = settings.data.Controller.Bias.Voltage.get('value')
                Constant_Output('Bias',bias_voltage_value)
                print(f"设置偏置电压: {bias_voltage_value}")
            except AttributeError as e:
                print(f"设置偏置电压失败: {e}")
        else:
            communicator.disconnect()
            return None, None, "初始化失败"
        
        # 5. 创建数据读取线程
        data_reader_thread = DataReaderThread(communicator)


        return communicator, data_reader_thread, status_message
            
    except Exception as e:
        print(f"初始化控制器时发生错误: {e}")
        if 'communicator' in locals() and communicator:
            communicator.disconnect()
        return None, None, f"初始化错误: {str(e)}"