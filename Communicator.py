# communication.py
# 通信抽象类
import pyvisa
import socket
import threading
import queue
import time
from abc import ABC, abstractmethod
from PyQt6.QtCore import QObject,pyqtSignal
import struct

# --- 配置常量 ---
# 串口参数（请根据实际情况修改）
SERIAL_RESOURCE_NAME = "USB0::0xFFFF::0xFFFB::2073W"
ENDIANNESS = ">"  # 大端字节序 (">" for big-endian, "<" for little-endian)
# --- 抽象通信器接口 ---
class IControllerCommunicator(ABC):
    """
    控制器通信器的抽象接口
    定义了与控制器通信所需的基本操作
    """
    _instance = None
    _lock = threading.Lock() # 用于单例模式的线程锁
    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super(IControllerCommunicator, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        self._loop_index = 0  # 初始化循环索引

    @abstractmethod
    def connect(self, config_params):
        """连接到控制器"""
        pass

    @abstractmethod
    def disconnect(self):
        """断开与控制器的连接"""
        pass

    @abstractmethod
    def write_raw(self, data_bytes):
        """写入原始字节数据到控制器"""
        pass
        
    # 添加新的抽象方法，专门用于发送指令
    @abstractmethod
    def send_command(self, command_num, command_value):
        """
        发送指令到控制器
        Args:
            command_num: 指令序号 (u8, 0-255)
            command_value: 指令值 (u32, 0-4294967295)
        Returns:
            bool: 发送成功返回True，否则返回False
        """
        pass

    @abstractmethod
    def read_bytes(self, num_bytes, timeout_ms=1000):
        """
        从控制器读取指定数量的字节
        参数:
            num_bytes: 要读取的字节数
            timeout_ms: 读取超时时间 (毫秒)
        返回:
            bytes: 读取到的字节数据
        抛出:
            TimeoutError: 读取超时
            IOError: 其他I/O错误
        """
        pass
    
    @abstractmethod
    def clear(self):
        """清空通信缓冲区"""
        pass

    @abstractmethod
    def is_connected(self):
        """检查是否已连接"""
        pass

    @abstractmethod
    def process_raw_data(self, raw_data):
        """
        处理原始数据并转换为物理值
        
        Args:
            raw_data: 从设备读取的原始数据
            
        Returns:
            tuple: (处理后的物理值数据, 更新后的loop_index)
        """
        pass

# --- 串口通信器实现 ---
class SerialCommunicator(IControllerCommunicator):
    """PyVISA 串口通信器的具体实现"""
    def __init__(self):
        super().__init__()  # 调用父类初始化方法
        self.rm = None
        self.instrument = None
        self._is_connected = False
        self._recording = False
        self._recorded_data = []
        print("SerialCommunicator 实例已创建")

    def connect(self, config_params):
        """
        连接到串口设备
        """
        resource_name = config_params.get('resource_name')
        timeout_ms = config_params.get('timeout_ms', 1000)
        
        try:
            self.rm = pyvisa.ResourceManager()
            self.instrument = self.rm.open_resource(resource_name)
            self.instrument.timeout = timeout_ms
            self._is_connected = True
            print(f"成功打开串口: {self.instrument.resource_name}")
            return True
        except pyvisa.errors.VisaIOError as e:
            print(f"SerialCommunicator 连接失败: {e}")
            self.disconnect() # 确保清理资源
            return False
        except Exception as e:
            print(f"SerialCommunicator 连接发生意外错误: {e}")
            self.disconnect()
            return False

    def disconnect(self):
        """断开串口连接"""
        if self.instrument:
            try:
                self.instrument.close()
                print("串口连接已关闭")
            except Exception as e:
                print(f"关闭串口时出错: {e}")
            finally:
                self.instrument = None
        if self.rm:
            try:
                self.rm.close()
                print("VISA资源管理器已释放")
            except Exception as e:
                print(f"关闭资源管理器时出错: {e}")
            finally:
                self.rm = None
        self._is_connected = False

    def write_raw(self, data_bytes):
        """写入原始字节数据到串口"""
        if not self.is_connected():
            raise IOError("串口未连接")
        self.instrument.write_raw(data_bytes)

    def read_bytes(self, num_bytes, timeout_ms=1000):
        """从串口读取指定数量的字节"""
        if not self.is_connected():
            raise IOError("串口未连接")
        original_timeout = self.instrument.timeout
        self.instrument.timeout = timeout_ms # 临时设置超时时间
        try:
            data = self.instrument.read_bytes(num_bytes)
            return data
        except pyvisa.errors.VisaIOError as e:
            if "Timeout" in str(e):
                raise TimeoutError(f"串口读取超时: {e}")
            else:
                raise IOError(f"串口读取错误: {e}")
        finally:
            self.instrument.timeout = original_timeout # 恢复原始超时时间

    def clear(self):
        """清空串口缓冲区"""
        if self.instrument:
            try:
                self.instrument.clear()
            except Exception as e:
                print(f"清空串口缓冲区失败: {e}")

    def is_connected(self):
        return self._is_connected and self.instrument is not None

    def start_recording(self):
        """开始记录接收到的数据"""
        self._recording = True
        self._recorded_data = []
        print("开始记录通信数据")
        
    def stop_recording(self):
        """停止记录并保存到TDMS文件"""
        if not self._recording:
            return False
            
        self._recording = False
        
        try:
            # 使用npTDMS保存数据
            from nptdms import TdmsWriter, ChannelObject
            import numpy as np
            import time
            
            filename = f"recorded_data_{int(time.time())}.tdms"
            
            with TdmsWriter(filename) as tdms_writer:
                # 将字节数据转换为适合保存的格式
                channel = ChannelObject('Recordings', 'raw_data', self._recorded_data)
                tdms_writer.write_segment([channel])
                
            print(f"已记录{len(self._recorded_data)}条数据到文件: {filename}")
            return True
            
        except Exception as e:
            print(f"保存记录数据失败: {e}")
            return False

    def process_raw_data(self, raw_data):
        """
        处理从串口接收的原始数据，转换为物理值
        
        Returns:
            tuple: (处理后的物理值数据, 更新后的loop_index)
        """
        from controllerutil import process_received_data, reshape_data
        
        # 使用controllerutil中的函数处理数据
        u32_values = process_received_data(ENDIANNESS, raw_data)
        if u32_values is None:
            return None, self._loop_index
            
        # 重塑数据并获取物理值
        processed_data, new_loop_index = reshape_data(u32_values, self._loop_index)
        self._loop_index = new_loop_index
        
        return processed_data, self._loop_index

    def send_command(self, command_num, command_value):
        """
        发送指令到串口设备
        Args:
            command_num: 指令序号 (u8, 0-255)
            command_value: 指令值 (u32, 0-4294967295)
        Returns:
            bool: 发送成功返回True，否则返回False
        """
        return dwrite(self, command_num, command_value)

# --- TCP/IP 通信器实现 ---
class TcpCommunicator(IControllerCommunicator):
    """TCP/IP 通信器的具体实现"""
    def __init__(self):
        super().__init__()  # 调用父类初始化方法
        self.socket = None
        self._is_connected = False
        self.receive_buffer = b''  # 接收缓冲区，用于处理不完整的帧
        self.MAX_BUFFER_SIZE = 32768  # 设置最大缓冲区大小为32KB
        self._loop_index = 0
        print("TcpCommunicator 实例已创建")
        
    def connect(self, config_params):
        """
        连接到TCP服务器
        
        Args:
            config_params: 包含连接参数的字典，必须包含'ip_address'和'port'
        
        Returns:
            bool: 连接是否成功
        """
        ip_address = config_params.get('ip_address')
        port = config_params.get('port')
        timeout_s = config_params.get('timeout_ms', 1000) / 1000.0 # 转换为秒
        
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(timeout_s)
            self.socket.connect((ip_address, port))
            self.socket.settimeout(1.0)  # 连接成功后设置为1秒超时，与网口通信测试一致
            self._is_connected = True
            print(f"成功连接到TCP: {ip_address}:{port}")
            return True
        except socket.timeout:
            print(f"TCP连接超时: {ip_address}:{port}")
            self.disconnect()
            return False
        except socket.error as e:
            print(f"TCP连接错误: {e}")
            self.disconnect()
            return False
        except Exception as e:
            print(f"TCP连接发生意外错误: {e}")
            self.disconnect()
            return False

    def disconnect(self):
        """断开TCP连接"""
        if self.socket:
            try:
                self.socket.shutdown(socket.SHUT_RDWR)
                self.socket.close()
                print("TCP连接已关闭")
            except Exception as e:
                print(f"关闭TCP连接时出错: {e}")
            finally:
                self.socket = None
        self._is_connected = False
        self.receive_buffer = b''  # 清空缓冲区

    def write_raw(self, data_bytes):
        """写入原始字节数据到TCP 这个不实现 只作为占位"""
        print(f"调用错误")
        return

    def read_bytes(self, num_bytes, timeout_ms=1000):
        """
        从TCP连接读取指定数量的字节
        """
        try:
            # 检查缓冲区大小，如果超过最大值，丢弃旧数据
            if len(self.receive_buffer) > self.MAX_BUFFER_SIZE:
                print(f"缓冲区过大({len(self.receive_buffer)}字节)，丢弃旧数据")
                # 只保留最后MAX_BUFFER_SIZE/2字节的数据
                self.receive_buffer = self.receive_buffer[-self.MAX_BUFFER_SIZE//2:]
            
            # 定义常量
            HEADER_BYTE = 0xBB
            FRAME_ID_OFFSET = 1
            FRAME_ID_LEN = 4
            ARM_ID_OFFSET = FRAME_ID_OFFSET + FRAME_ID_LEN
            ARM_ID_LEN = 12
            HOVER_RESULT_OFFSET = ARM_ID_OFFSET + ARM_ID_LEN
            HOVER_RESULT_LEN = 4
            HOVER_COUNT_OFFSET = HOVER_RESULT_OFFSET + HOVER_RESULT_LEN
            HOVER_COUNT_LEN = 4
            NUM_GROUPS_OFFSET = HOVER_COUNT_OFFSET + HOVER_COUNT_LEN
            NUM_GROUPS_LEN = 4
            MAIN_HEADER_TOTAL_LEN = 1 + FRAME_ID_LEN + ARM_ID_LEN + HOVER_RESULT_LEN + HOVER_COUNT_LEN + NUM_GROUPS_LEN
            DATA_GROUP_LEN = 32
            FOOTER_BYTES = b'\x0D\x0A'
            FOOTER_LEN = 2
            BUFFER_SIZE = 4096
            
            start_time = time.time()
            
            try:
                while (time.time() - start_time) * 1000 < timeout_ms:
                    try:
                        # 尝试从缓冲区中找到一个完整的帧
                        frame_data = self._process_buffer()
                        if frame_data:
                            return frame_data
                        
                        # 如果没有找到完整帧，接收更多数据
                        data = self.socket.recv(BUFFER_SIZE)
                        if not data:
                            raise IOError("TCP连接已关闭")
                        
                        # print(f"接收到{len(data)}字节数据")
                        self.receive_buffer += data
                        
                    except socket.timeout:
                        # 接收超时，继续尝试
                        print("接收超时，继续尝试...")
                        continue
                
                # 超时未找到完整帧
                raise TimeoutError(f"TCP读取超时({timeout_ms}ms)，未找到完整帧")
                
            except Exception as e:
                print(f"TCP读取错误: {e}")
                raise
        
        except Exception as e:
            print(f"TCP读取错误: {e}")
            raise
    
    def _process_buffer(self):
        """
        处理接收缓冲区，尝试识别和解析完整的帧
        直接从网口通信测试.py中迁移
        
        Returns:
            bytes: 找到的完整帧，如果没有找到则返回None
        """
        # 定义常量
        HEADER_BYTE = 0xBB
        FRAME_ID_OFFSET = 1
        FRAME_ID_LEN = 4
        ARM_ID_OFFSET = FRAME_ID_OFFSET + FRAME_ID_LEN
        ARM_ID_LEN = 12
        HOVER_RESULT_OFFSET = ARM_ID_OFFSET + ARM_ID_LEN
        HOVER_RESULT_LEN = 4
        HOVER_COUNT_OFFSET = HOVER_RESULT_OFFSET + HOVER_RESULT_LEN
        HOVER_COUNT_LEN = 4
        NUM_GROUPS_OFFSET = HOVER_COUNT_OFFSET + HOVER_COUNT_LEN
        NUM_GROUPS_LEN = 4
        MAIN_HEADER_TOTAL_LEN = 1 + FRAME_ID_LEN + ARM_ID_LEN + HOVER_RESULT_LEN + HOVER_COUNT_LEN + NUM_GROUPS_LEN
        DATA_GROUP_LEN = 32
        FOOTER_BYTES = b'\x0D\x0A'
        FOOTER_LEN = 2
        BUFFER_SIZE = 4096
        
        # 查找帧头字节 0xBB
        header_index = self.receive_buffer.find(bytes([HEADER_BYTE]))
        if header_index == -1:
            # 如果缓冲区过大且没有帧头，则清空缓冲区，避免内存溢出
            if len(self.receive_buffer) > BUFFER_SIZE * 2:
                print("缓冲区过大且无帧头，清空缓冲区")
                self.receive_buffer = b''
            return None  # 没有找到帧头
        
        # 移除帧头之前的无效数据
        if header_index > 0:
            print(f"跳过帧头前的{header_index}字节")
            self.receive_buffer = self.receive_buffer[header_index:]
            header_index = 0  # 帧头现在在缓冲区的起始位置
        
        # 检查是否有足够的数据用于解析主头部（固定部分）
        if len(self.receive_buffer) < MAIN_HEADER_TOTAL_LEN:
            print(f"数据不足以解析主头部: {len(self.receive_buffer)}/{MAIN_HEADER_TOTAL_LEN}")
            return None  # 数据不足
        
        # 提取 num_groups 以确定完整帧的预期长度
        try:
            num_groups_data = self.receive_buffer[NUM_GROUPS_OFFSET : NUM_GROUPS_OFFSET + NUM_GROUPS_LEN]
            num_groups = struct.unpack('<I', num_groups_data)[0]
            
            # 检查 num_groups 是否在有效范围内
            if not (0 <= num_groups <= 256):
                print(f"无效的数据组数: {num_groups}")
                self.receive_buffer = self.receive_buffer[1:]  # 跳过当前帧头
                return None
                
            # print(f"数据组数: {num_groups}")
        except struct.error:
            print("解析数据组数时出错")
            self.receive_buffer = self.receive_buffer[1:]  # 跳过当前帧头
            return None
        
        # 计算完整帧的预期总长度
        expected_frame_len = MAIN_HEADER_TOTAL_LEN + (num_groups * DATA_GROUP_LEN) + FOOTER_LEN
        
        # 检查缓冲区是否有足够的数据构成一个完整的帧
        if len(self.receive_buffer) < expected_frame_len:
            # print(f"数据不足以构成完整帧: {len(self.receive_buffer)}/{expected_frame_len}")
            return None  # 数据不足
        
        # 提取一个潜在的完整帧
        full_frame_data = self.receive_buffer[:expected_frame_len]
        
        # 检查帧尾是否匹配
        if full_frame_data[-FOOTER_LEN:] != FOOTER_BYTES:
            print(f"帧尾不匹配: {full_frame_data[-FOOTER_LEN:].hex()} != {FOOTER_BYTES.hex()}")
            self.receive_buffer = self.receive_buffer[1:]  # 跳过当前帧头
            return None
        
        # print(f"找到完整帧: 长度={expected_frame_len}字节, 数据组数={num_groups}")
        
        # 从缓冲区中移除已处理的帧
        self.receive_buffer = self.receive_buffer[expected_frame_len:]
        
        return full_frame_data

    def clear(self):
        """TCP通常不需要显式清空缓冲区，但我们可以清空内部缓冲区"""
        # 简单地重置缓冲区，不进行额外的socket读取
        old_size = len(self.receive_buffer)
        self.receive_buffer = b''
        print(f"清空缓冲区：释放了{old_size}字节")
        return True

    def is_connected(self):
        """检查是否已连接"""
        return self._is_connected and self.socket is not None

    def process_raw_data(self, raw_data):
        """
        处理从TCP接收的原始数据，直接转换为物理值
        
        Returns:
            tuple: (处理后的物理值数据, 更新后的loop_index)
        """
        try:
            # 解析帧数据
            parsed_data = self._parse_tcp_frame(raw_data)
            if not parsed_data:
                return None, self._loop_index
                
            # 将解析后的数据转换为与串口通信相同的格式，便于后续处理
            physical_values = self._convert_to_physical_values(parsed_data)
            
            # 更新loop_index
            if physical_values and len(physical_values) > 0:
                self._loop_index += 1
            return physical_values, self._loop_index
        except Exception as e:
            print(f"TCP数据处理错误: {e}")
            import traceback
            traceback.print_exc()
            return None, self._loop_index
            
    def _parse_tcp_frame(self, frame_data):
        """
        解析TCP数据帧，类似于网口通信测试.py中的parse_frame函数
        """
        import struct
        
        # 定义常量
        HEADER_BYTE = 0xBB
        FRAME_ID_LEN = 4
        ARM_ID_LEN = 12
        HOVER_RESULT_LEN = 4
        HOVER_COUNT_LEN = 4
        NUM_GROUPS_LEN = 4
        FOOTER_LEN = 2
        
        parsed_data = {}
        offset = 0
        
        # 包头
        parsed_data['包头'] = hex(frame_data[offset])
        offset += 1
        
        # 帧ID (uint32_t, 小端序)
        parsed_data['帧ID'] = struct.unpack('<I', frame_data[offset:offset+FRAME_ID_LEN])[0]
        offset += FRAME_ID_LEN
        
        # ARM ID (3 x uint32_t, 小端序)
        arm_ids_tuple = struct.unpack('<III', frame_data[offset:offset+ARM_ID_LEN])
        parsed_data['ARM ID'] = [f"{uid:08X}" for uid in arm_ids_tuple]
        offset += ARM_ID_LEN
        
        # 悬停结果 (uint32_t, 小端序)
        parsed_data['悬停结果'] = struct.unpack('<I', frame_data[offset:offset+HOVER_RESULT_LEN])[0]
        offset += HOVER_RESULT_LEN
        
        # 悬停计数值 (uint32_t, 小端序)
        parsed_data['悬停计数值'] = struct.unpack('<I', frame_data[offset:offset+HOVER_COUNT_LEN])[0]
        offset += HOVER_COUNT_LEN
        
        # 数据组数n (uint32_t, 小端序)
        num_groups = struct.unpack('<I', frame_data[offset:offset+NUM_GROUPS_LEN])[0]
        parsed_data['数据组数n'] = num_groups
        offset += NUM_GROUPS_LEN
        
        # 数据组解析
        parsed_data['数据组'] = []
        for i in range(num_groups):
            group_data = {}
            
            # PWM计数值 (int32_t, 小端序)
            group_data['PWM计数值'] = struct.unpack('<i', frame_data[offset:offset+4])[0]
            offset += 4
            
            # 模拟输出1-4 (4 x int32_t, 小端序)
            group_data['模拟输出1_uV'] = struct.unpack('<i', frame_data[offset:offset+4])[0]
            offset += 4
            group_data['模拟输出2_uV'] = struct.unpack('<i', frame_data[offset:offset+4])[0]
            offset += 4
            group_data['模拟输出3_uV'] = struct.unpack('<i', frame_data[offset:offset+4])[0]
            offset += 4
            group_data['模拟输出4_uV'] = struct.unpack('<i', frame_data[offset:offset+4])[0]
            offset += 4
            
            # 采样间隔 (uint32_t, 小端序)
            group_data['采样间隔_us'] = struct.unpack('<I', frame_data[offset:offset+4])[0]
            offset += 4
            
            # 模拟输入1-4 (4 x int16_t, 小端序)
            for j in range(1, 5):
                raw_ai = struct.unpack('<h', frame_data[offset:offset+2])[0]
                group_data[f'模拟输入{j}_原始值'] = raw_ai
                group_data[f'模拟输入{j}_V'] = raw_ai * 10.0 / 32768.0
                offset += 2
            
            parsed_data['数据组'].append(group_data)
        
        # 包尾
        parsed_data['包尾'] = hex(struct.unpack('>H', frame_data[-FOOTER_LEN:])[0])
        
        return parsed_data
        
    def _convert_to_physical_values(self, parsed_data):
        """
        将解析后的TCP数据转换为与串口通信相同格式的物理值
        返回格式应与controllerutil.reshape_data相同
        
        映射关系:
        串口 - 网口
        ID - 帧ID
        PWMCOUNTER - pwm_cnt
        TS0 - AI2
        TS1 - AI3
        Bias - AO1(模拟输出1)
        Piezo - AO2
        AO2 - AO3
        AO3 - AO4
        dT - dT(采样间隔)
        AI0 - AI0
        AI1 - AI1
        """
        from settings import settings
        import numpy as np
        
        if not parsed_data:
            return None
        # print(f'_convert_to_physical_values: 数据组数={parsed_data.get("数据组数n", 0)}')
        # 创建物理值列表 (每个列表对应一个物理量的所有样本)
        id_list = []
        motor_position_list = []
        dut_temp_list = []
        amb_temp_list = []
        bias_voltage_list = []
        piezo_voltage_list = []
        re_voltage_list = []
        ce_voltage_list = []
        dt_list = []
        ai0_voltage_list = []
        ai1_voltage_list = []
        log_conductance_list = []
        current_list = []
        motor_pos_physical_list = []
        piezo_pos_physical_list = []
        
        # 处理每个数据组
        for group in parsed_data['数据组']:
            # 提取基本值
            frame_id = parsed_data['帧ID']
            motor_position = group['PWM计数值']
            bias_voltage = group['模拟输出1_uV'] * 1e-6  # uV转V
            piezo_voltage = group['模拟输出2_uV'] * 1e-6  # uV转V
            re_voltage = group['模拟输出3_uV'] * 1e-6     # uV转V
            ce_voltage = group['模拟输出4_uV'] * 1e-6     # uV转V
            dt = group['采样间隔_us']
            ai0_voltage = group['模拟输入1_V']
            ai1_voltage = group['模拟输入2_V']
            
            # 温度传感器值 (使用模拟输入3和4作为温度传感器)
            dut_temp = group['模拟输入3_V'] * 10.0  # 简单转换，实际可能需要更复杂的公式
            amb_temp = group['模拟输入4_V'] * 10.0  # 简单转换，实际可能需要更复杂的公式
            
            # 计算电流和电导率
            from controllerutil import calculate_current_and_conductance
            current_mA, conductance_S, log_conductance = calculate_current_and_conductance(
                settings, ai0_voltage, bias_voltage
            )
            current_A = current_mA * 1e-3  # mA转A
            
            # 计算物理位置 (如果需要)
            motor_pos_physical = motor_position * 0
            piezo_pos_physical = piezo_voltage * 0
            
            # 添加到列表
            id_list.append(frame_id)
            motor_position_list.append(motor_position)
            dut_temp_list.append(dut_temp)
            amb_temp_list.append(amb_temp)
            bias_voltage_list.append(bias_voltage)
            piezo_voltage_list.append(piezo_voltage)
            re_voltage_list.append(re_voltage)
            ce_voltage_list.append(ce_voltage)
            dt_list.append(dt)
            ai0_voltage_list.append(ai0_voltage)
            ai1_voltage_list.append(ai1_voltage)
            log_conductance_list.append(log_conductance)
            current_list.append(current_A)
            motor_pos_physical_list.append(motor_pos_physical)
            piezo_pos_physical_list.append(piezo_pos_physical)
        
        # 返回转置后的数据 (与reshape_data的输出格式相同)
        result = [
            id_list,
            motor_position_list,
            dut_temp_list,
            amb_temp_list,
            bias_voltage_list,
            piezo_voltage_list,
            re_voltage_list,
            ce_voltage_list,
            dt_list,
            ai0_voltage_list,
            ai1_voltage_list,
            log_conductance_list,
            current_list,
            motor_pos_physical_list,
            piezo_pos_physical_list
        ]
        return result

    def send_command(self, command_num, command_value):
        """发送指令到下位机"""
        if not self.is_connected():
            print("状态: 未连接，无法发送指令 ❌")
            return

        try:
            # 验证输入范围
            # 指令序号是u8，范围0-255
            if not (0 <= command_num <= 255):
                print("状态: 指令序号超出范围 (0-255) ❌")
            # 指令值是u32，范围0-4294967295
            if not (0 <= command_value <= 0xFFFFFFFF):
                print("状态: 指令值超出范围 (0-4294967295) ❌")

        except ValueError:
            print("请输入有效的数字")
            return

        # 构建指令帧
        # 格式: 包头(0xBB) + 指令序号(u8) + 指令值(u32小端序) + 包尾(0x0D 0x0A)
        # 示例: (1,1) -> BB 01 01 00 00 00 0D 0A
        try:
            frame = bytearray()
            frame.append(0xBB)  # 包头
            frame.append(command_num)  # 指令序号 (u8)
            frame.extend(struct.pack('<I', command_value))  # 指令值 (小端序u32)
            frame.extend(b'\x0D\x0A')  # 包尾
            # 发送指令
            if self.socket:
                self.socket.send(frame)
                hex_string = ' '.join(f'{byte:02X}' for byte in frame)
                print(f"发送指令帧: {hex_string}")

            else:
                print("套接字未连接")
        except Exception as e:
            print(f"发送指令时发生错误: {e}")

# --- 信号类 ---
class SerialSignals(QObject):
    data_ready = pyqtSignal(object)  # 现在发送(raw_data, processed_data)元组
    error = pyqtSignal(str)
    no_data = pyqtSignal()

# --- 通用数据读取线程类 ---
class DataReaderThread(threading.Thread):
    def __init__(self, communicator: IControllerCommunicator, queue_size=200):
        super().__init__()
        self.communicator = communicator
        self.running = True
        self.signals = SerialSignals() # 信号类名可以不变，或者根据应用改名
        self.data_queue = queue.Queue(maxsize=queue_size)
        self.daemon = True
    
    def run(self):
        while self.running:
            try:
                if not self.communicator.is_connected():
                    time.sleep(0.1)
                    continue
                
                # 读取数据
                received_data = self.communicator.read_bytes(123 * 4, timeout_ms=1000)

                if received_data and len(received_data) > 0:
                    if self.data_queue.full():
                        try:
                            self.data_queue.get_nowait()
                        except queue.Empty:
                            pass
                    # 处理原始数据并转换为物理值
                    processed_data, _ = self.communicator.process_raw_data(received_data)
                    # 将原始数据和处理后的数据都放入队列
                    self.data_queue.put((received_data, processed_data))
                    # 发送原始数据和处理后的数据
                    self.signals.data_ready.emit(processed_data)
                else:
                    print("未读取到数据")
                    self.signals.no_data.emit()
            except TimeoutError as e:
                print(f"读取数据超时: {e}")
                self.signals.error.emit(f"读取超时: {str(e)}")
                time.sleep(0.5)
            except IOError as e:
                print(f"通信错误: {e}")
                self.signals.error.emit(f"通信错误: {str(e)}")
                time.sleep(0.5)
            except Exception as e:
                print(f"读取线程发生未知错误: {e}")
                self.signals.error.emit(f"未知错误: {str(e)}")
                time.sleep(0.5)
    
    def stop(self):
        self.running = False
        self.join(timeout=1.0)

class VirtualCommunicator(IControllerCommunicator):
    """简单的虚拟通信器，从文本文件读取记录的字节流"""
    def __init__(self):
        super().__init__()  # 调用父类初始化方法
        self._connected = False
        self._file = None
        
    def connect(self, config_params):
        """连接到虚拟设备（打开记录文件）"""
        try:
            filename = config_params.get('filename', 'data_record.txt')
            self._file = open(filename, 'rb')
            self._connected = True
            print(f"已连接到虚拟设备，数据源: {filename}")
            return True
        except Exception as e:
            print(f"连接到虚拟设备失败: {e}")
            self._connected = False
            return False
    
    def disconnect(self):
        """断开连接"""
        if self._file:
            self._file.close()
        self._connected = False
        return True
    
    def is_connected(self):
        """检查是否已连接"""
        return self._connected and self._file is not None
    
    def read_bytes(self, num_bytes, timeout_ms=1000):
        """从记录文件读取数据"""
        if not self.is_connected():
            raise IOError("虚拟设备未连接")
        
        try:
            # 读取长度字段（4字节）
            length_bytes = self._file.read(4)
            if not length_bytes or len(length_bytes) < 4:
                # 文件结束，回到开始
                self._file.seek(0)
                length_bytes = self._file.read(4)
                if not length_bytes:
                    return None  # 文件为空
            
            # 解析长度
            data_length = int.from_bytes(length_bytes, byteorder='big')
            
            # 读取实际数据
            data = self._file.read(data_length)
            if len(data) < data_length:
                # 数据不完整，回到开始
                self._file.seek(0)
                return self.read_bytes(num_bytes, timeout_ms)
            
            # 模拟延迟
            import time
            time.sleep(5 / 1000.0)
            
            return data
            
        except Exception as e:
            print(f"从虚拟设备读取数据失败: {e}")
            return None
    
    def write_raw(self, data):
        """模拟写入操作（实际上不做任何事）"""
        print(f"虚拟设备收到命令: {data.hex()}")
        return True
    
    def clear(self):
        """清除缓冲区（虚拟操作）"""
        return True

    def process_raw_data(self, raw_data):
        """
        处理从虚拟设备读取的原始数据，转换为物理值
        与SerialCommunicator的处理方式相同
        
        Returns:
            tuple: (处理后的物理值数据, 更新后的loop_index)
        """
        from controllerutil import process_received_data, reshape_data
        # 使用controllerutil中的函数处理数据
        u32_values = process_received_data(ENDIANNESS, raw_data)
        
        if u32_values is None:
            return None, self._loop_index
        # 重塑数据并获取物理值
        processed_data, new_loop_index = reshape_data(u32_values, self._loop_index)
        self._loop_index = new_loop_index
        
        return processed_data, self._loop_index

    def send_command(self, command_num, command_value):
        """发送指令到虚拟设备"""
        return dwrite(self, command_num, command_value)
