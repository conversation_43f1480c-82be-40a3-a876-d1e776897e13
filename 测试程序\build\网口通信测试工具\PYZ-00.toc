('C:\\Users\\<USER>\\Desktop\\项目\\python复现评估\\python重写\\串口读取 '
 'v1.4\\测试程序\\build\\网口通信测试工具\\PYZ-00.pyz',
 [('PyQt6',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyQt6\\__init__.py',
   'PYMODULE-2'),
  ('_compat_pickle',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\_compat_pickle.py',
   'PYMODULE-2'),
  ('_compression',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\_compression.py',
   'PYMODULE-2'),
  ('_py_abc',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\_py_abc.py',
   'PYMODULE-2'),
  ('_pydatetime',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\_pydatetime.py',
   'PYMODULE-2'),
  ('_pydecimal',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\_pydecimal.py',
   'PYMODULE-2'),
  ('_pyi_rth_utils',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE-2'),
  ('_pyi_rth_utils.qt',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE-2'),
  ('_strptime',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\_strptime.py',
   'PYMODULE-2'),
  ('_threading_local',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\_threading_local.py',
   'PYMODULE-2'),
  ('argparse',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\argparse.py',
   'PYMODULE-2'),
  ('ast',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\ast.py',
   'PYMODULE-2'),
  ('base64',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\base64.py',
   'PYMODULE-2'),
  ('bisect',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\bisect.py',
   'PYMODULE-2'),
  ('bz2',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\bz2.py',
   'PYMODULE-2'),
  ('calendar',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\calendar.py',
   'PYMODULE-2'),
  ('contextlib',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\contextlib.py',
   'PYMODULE-2'),
  ('contextvars',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\contextvars.py',
   'PYMODULE-2'),
  ('copy',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\copy.py',
   'PYMODULE-2'),
  ('csv',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\csv.py',
   'PYMODULE-2'),
  ('dataclasses',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\dataclasses.py',
   'PYMODULE-2'),
  ('datetime',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\datetime.py',
   'PYMODULE-2'),
  ('decimal',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\decimal.py',
   'PYMODULE-2'),
  ('dis',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\dis.py',
   'PYMODULE-2'),
  ('email',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\__init__.py',
   'PYMODULE-2'),
  ('email._encoded_words',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\_encoded_words.py',
   'PYMODULE-2'),
  ('email._header_value_parser',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\_header_value_parser.py',
   'PYMODULE-2'),
  ('email._parseaddr',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\_parseaddr.py',
   'PYMODULE-2'),
  ('email._policybase',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\_policybase.py',
   'PYMODULE-2'),
  ('email.base64mime',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\base64mime.py',
   'PYMODULE-2'),
  ('email.charset',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\charset.py',
   'PYMODULE-2'),
  ('email.contentmanager',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\contentmanager.py',
   'PYMODULE-2'),
  ('email.encoders',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\encoders.py',
   'PYMODULE-2'),
  ('email.errors',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\errors.py',
   'PYMODULE-2'),
  ('email.feedparser',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\feedparser.py',
   'PYMODULE-2'),
  ('email.generator',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\generator.py',
   'PYMODULE-2'),
  ('email.header',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\header.py',
   'PYMODULE-2'),
  ('email.headerregistry',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\headerregistry.py',
   'PYMODULE-2'),
  ('email.iterators',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\iterators.py',
   'PYMODULE-2'),
  ('email.message',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\message.py',
   'PYMODULE-2'),
  ('email.parser',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\parser.py',
   'PYMODULE-2'),
  ('email.policy',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\policy.py',
   'PYMODULE-2'),
  ('email.quoprimime',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\quoprimime.py',
   'PYMODULE-2'),
  ('email.utils',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\email\\utils.py',
   'PYMODULE-2'),
  ('fnmatch',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\fnmatch.py',
   'PYMODULE-2'),
  ('fractions',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\fractions.py',
   'PYMODULE-2'),
  ('getopt',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\getopt.py',
   'PYMODULE-2'),
  ('gettext',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\gettext.py',
   'PYMODULE-2'),
  ('gzip',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\gzip.py',
   'PYMODULE-2'),
  ('hashlib',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\hashlib.py',
   'PYMODULE-2'),
  ('importlib',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\__init__.py',
   'PYMODULE-2'),
  ('importlib._abc',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\_abc.py',
   'PYMODULE-2'),
  ('importlib._bootstrap',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE-2'),
  ('importlib._bootstrap_external',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE-2'),
  ('importlib.abc',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\abc.py',
   'PYMODULE-2'),
  ('importlib.machinery',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\machinery.py',
   'PYMODULE-2'),
  ('importlib.metadata',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE-2'),
  ('importlib.metadata._adapters',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE-2'),
  ('importlib.metadata._collections',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE-2'),
  ('importlib.metadata._functools',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE-2'),
  ('importlib.metadata._itertools',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE-2'),
  ('importlib.metadata._meta',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE-2'),
  ('importlib.metadata._text',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE-2'),
  ('importlib.readers',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\readers.py',
   'PYMODULE-2'),
  ('importlib.resources',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE-2'),
  ('importlib.resources._adapters',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE-2'),
  ('importlib.resources._common',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE-2'),
  ('importlib.resources._itertools',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE-2'),
  ('importlib.resources._legacy',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE-2'),
  ('importlib.resources.abc',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE-2'),
  ('importlib.resources.readers',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE-2'),
  ('importlib.util',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\importlib\\util.py',
   'PYMODULE-2'),
  ('inspect',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\inspect.py',
   'PYMODULE-2'),
  ('ipaddress',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\ipaddress.py',
   'PYMODULE-2'),
  ('logging',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\logging\\__init__.py',
   'PYMODULE-2'),
  ('lzma',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\lzma.py',
   'PYMODULE-2'),
  ('numbers',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\numbers.py',
   'PYMODULE-2'),
  ('opcode',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\opcode.py',
   'PYMODULE-2'),
  ('pathlib',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\pathlib.py',
   'PYMODULE-2'),
  ('pickle',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\pickle.py',
   'PYMODULE-2'),
  ('pkgutil',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\pkgutil.py',
   'PYMODULE-2'),
  ('pprint',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\pprint.py',
   'PYMODULE-2'),
  ('py_compile',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\py_compile.py',
   'PYMODULE-2'),
  ('quopri',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\quopri.py',
   'PYMODULE-2'),
  ('random',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\random.py',
   'PYMODULE-2'),
  ('selectors',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\selectors.py',
   'PYMODULE-2'),
  ('shutil',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\shutil.py',
   'PYMODULE-2'),
  ('signal',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\signal.py',
   'PYMODULE-2'),
  ('socket',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\socket.py',
   'PYMODULE-2'),
  ('statistics',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\statistics.py',
   'PYMODULE-2'),
  ('string',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\string.py',
   'PYMODULE-2'),
  ('stringprep',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\stringprep.py',
   'PYMODULE-2'),
  ('subprocess',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\subprocess.py',
   'PYMODULE-2'),
  ('tarfile',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\tarfile.py',
   'PYMODULE-2'),
  ('tempfile',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\tempfile.py',
   'PYMODULE-2'),
  ('textwrap',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\textwrap.py',
   'PYMODULE-2'),
  ('threading',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\threading.py',
   'PYMODULE-2'),
  ('token',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\token.py',
   'PYMODULE-2'),
  ('tokenize',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\tokenize.py',
   'PYMODULE-2'),
  ('tracemalloc',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\tracemalloc.py',
   'PYMODULE-2'),
  ('typing',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\typing.py',
   'PYMODULE-2'),
  ('urllib',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\urllib\\__init__.py',
   'PYMODULE-2'),
  ('urllib.parse',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\urllib\\parse.py',
   'PYMODULE-2'),
  ('zipfile',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\zipfile\\__init__.py',
   'PYMODULE-2'),
  ('zipfile._path',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE-2'),
  ('zipfile._path.glob',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE-2'),
  ('zipimport',
   'D:\\anaconda3\\envs\\my_pyqt6_env_stmproject\\Lib\\zipimport.py',
   'PYMODULE-2')])
