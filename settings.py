import json

class DotDict:
    """支持点语法访问的字典"""
    def __init__(self, data=None):
        self._data = data or {}
    
    def __getattr__(self, key):
        if key in self._data:
            value = self._data[key]
            if isinstance(value, dict):
                return DotDict(value)
            return value
        raise AttributeError(f"配置中不存在 '{key}' 属性")
    
    def __setattr__(self, key, value):
        if key == "_data":
            super().__setattr__(key, value)
        else:
            self._data[key] = value

    def __getitem__(self, key):
        """支持字典的[]访问语法，如 obj['key']"""
        if key in self._data:
            value = self._data[key]
            if isinstance(value, dict):
                return DotDict(value)
            return value
        raise KeyError(f"配置中不存在 '{key}' 键")    
    def __setitem__(self, key, value):
        """支持字典的[]设置语法，如 obj['key'] = value"""
        self._data[key] = value

    def __contains__(self, key):
        return key in self._data
    
    def get(self, key, default=None):
        """
        获取配置值，与__getattr__不同的是可以接受默认值
        仅用于不关键的配置项，建议尽量不使用
        """
        if key in self._data:
            return self._data[key]
        if default is not None:
            print(f"警告: 配置中不存在 '{key}' 属性，使用默认值 {default}")
            return default
        raise KeyError(f"配置中不存在 '{key}' 键")
    
    def __repr__(self):
        return repr(self._data)
    
    def to_dict(self):
        """返回原始字典"""
        return self._data

class Settings:
    """全局设置类"""
    def __init__(self):
        """初始化空设置"""
        self.data = DotDict()
        self._temp_data = None  # 临时存储修改后的设置
        self.lastcontrollersetting = None  # 缓存上次的Controller设置
    
    def load_from_json(self, json_data):
        """
        从JSON数据加载设置
        Args:
            json_data: 字典格式的JSON数据
        """
        if not json_data:
            raise ValueError("无效的JSON数据")

        self.data = DotDict(json_data)
        # 创建深拷贝用于临时修改
        self._temp_data = DotDict(json.loads(json.dumps(json_data)))

        # 初始化Controller设置缓存
        if 'Controller' in json_data:
            self.lastcontrollersetting = DotDict(json.loads(json.dumps(json_data['Controller'])))
    
    def get_value(self, path):
        """
        通过路径获取嵌套值,python自带的方法
        Args:
            path: 点分隔的路径，例如 'Controller.Motor.State.value'
        Returns:
            找到的值
        Raises:
            AttributeError: 如果路径中任何部分不存在
        """
        parts = path.split('.')
        current = self.data
        
        for part in parts:
            try:
                current = getattr(current, part)
            except AttributeError:
                current = current[part]
                
        return current
    
    def update_temp(self, path, value):
        """
        更新临时设置，不持久化到文件
        Args:
            path: 点分隔的路径，例如 'Controller.Bias.Voltage.value'
            value: 要设置的值
        """
        if not self._temp_data:
            raise ValueError("临时设置数据未初始化，请先调用load_from_json")
            
        parts = path.split('.')
        current = self._temp_data
        
        # 遍历路径到倒数第二个部分
        for part in parts[:-1]:
            if part not in current._data:
                current._data[part] = {}
            current = getattr(current, part)
        
        # 设置最后一个部分的值
        setattr(current, parts[-1], value)
        
    def commit_temp(self):
        """将临时设置提交到主设置，但不持久化到文件"""
        if not self._temp_data:
            raise ValueError("临时设置数据未初始化，请先调用load_from_json")
        
        # 将临时设置复制到主设置
        self.data = DotDict(json.loads(json.dumps(self._temp_data._data)))
        
    def reset_temp(self):
        """重置临时设置为当前主设置"""
        if not self.data._data:
            return
            
        # 重新从主设置创建临时设置的深拷贝
        self._temp_data = DotDict(json.loads(json.dumps(self.data._data)))
    
    def to_dict(self):
        """将当前主设置转换为字典"""
        return self.data.to_dict()
        
    def save_to_file(self, filepath):
        """
        将当前设置保存到文件（当需要时才调用）
        Args:
            filepath: 文件路径
        """
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.data.to_dict(), f, indent=4)
            print(f"设置已保存到 {filepath}")
            return True
        except Exception as e:
            print(f"保存设置到文件时出错: {e}")
            import traceback
            traceback.print_exc()
            return False

    def controllerWrite(self, communicator=None):
        """
        比较当前Controller设置与缓存的差异，只写入有变化的寄存器
        Args:
            communicator: 通信器对象，如果为None则从HDControll模块获取
        Returns:
            bool: 写入成功返回True，失败返回False
        """
        if not self.data or 'Controller' not in self.data._data:
            print("警告: 没有Controller设置数据")
            return False

        # 获取通信器
        if communicator is None:
            try:
                import HDControll
                communicator = HDControll._communicator
                if communicator is None or not communicator.is_connected():
                    print("错误: 通信器未设置或未连接")
                    return False
            except ImportError:
                print("错误: 无法导入HDControll模块")
                return False

        current_controller = self.data.Controller

        # 如果没有缓存，说明是第一次写入，写入所有设置
        if self.lastcontrollersetting is None:
            print("首次写入Controller设置，写入所有参数")
            return self._write_all_controller_settings(communicator, current_controller)

        # 比较差异并收集需要写入的项目
        write_pairs = []
        self._collect_differences(current_controller._data, self.lastcontrollersetting._data, write_pairs)

        if not write_pairs:
            print("Controller设置无变化，跳过写入")
            return True

        print(f"检测到 {len(write_pairs)} 个Controller设置变化，开始写入...")

        # 批量写入变化的设置
        from controllerutil import multi_dwrite
        success = multi_dwrite(communicator, write_pairs)

        if success:
            # 更新缓存
            self.lastcontrollersetting = DotDict(json.loads(json.dumps(current_controller._data)))
            print("Controller设置差异写入成功")
        else:
            print("Controller设置差异写入失败")

        return success

    def _collect_differences(self, current_data, cached_data, write_pairs, path=""):
        """
        递归比较两个字典，收集有差异的item和value对
        Args:
            current_data: 当前设置数据
            cached_data: 缓存的设置数据
            write_pairs: 用于收集(item, value)对的列表
            path: 当前路径（用于调试）
        """
        for key, current_value in current_data.items():
            current_path = f"{path}.{key}" if path else key

            if isinstance(current_value, dict):
                # 如果是字典，递归比较
                cached_value = cached_data.get(key, {})
                if isinstance(cached_value, dict):
                    self._collect_differences(current_value, cached_value, write_pairs, current_path)
                else:
                    # 缓存中不是字典，说明结构变化了，需要写入所有子项
                    self._collect_all_items_from_dict(current_value, write_pairs, current_path)
            else:
                # 如果是叶子节点，检查是否有item和value字段
                if key in cached_data:
                    cached_value = cached_data[key]
                    if current_value != cached_value:
                        # 值有变化，检查父级是否有item字段
                        parent_data = current_data
                        if isinstance(parent_data, dict) and 'item' in parent_data and 'value' in parent_data:
                            if key == 'value':  # 只有value变化时才写入
                                item = parent_data['item']
                                value = current_value
                                write_pairs.append((item, value))
                                print(f"检测到变化: {current_path} = {value} (item={item})")
                else:
                    # 缓存中没有这个键，说明是新增的
                    parent_data = current_data
                    if isinstance(parent_data, dict) and 'item' in parent_data and 'value' in parent_data:
                        if key == 'value':
                            item = parent_data['item']
                            value = current_value
                            write_pairs.append((item, value))
                            print(f"检测到新增: {current_path} = {value} (item={item})")

    def _collect_all_items_from_dict(self, data_dict, write_pairs, path=""):
        """
        从字典中收集所有的item和value对
        Args:
            data_dict: 要处理的字典
            write_pairs: 用于收集(item, value)对的列表
            path: 当前路径（用于调试）
        """
        for key, value in data_dict.items():
            current_path = f"{path}.{key}" if path else key

            if isinstance(value, dict):
                # 检查是否是item-value对
                if 'item' in value and 'value' in value:
                    item = value['item']
                    val = value['value']
                    write_pairs.append((item, val))
                    print(f"收集设置项: {current_path} = {val} (item={item})")
                else:
                    # 递归处理子字典
                    self._collect_all_items_from_dict(value, write_pairs, current_path)

    def _write_all_controller_settings(self, communicator, controller_data):
        """
        写入所有Controller设置
        Args:
            communicator: 通信器对象
            controller_data: Controller设置数据
        Returns:
            bool: 写入成功返回True
        """
        write_pairs = []
        self._collect_all_items_from_dict(controller_data._data, write_pairs)

        if not write_pairs:
            print("没有找到可写入的Controller设置项")
            return True

        print(f"写入所有Controller设置，共 {len(write_pairs)} 项")

        from controllerutil import multi_dwrite
        success = multi_dwrite(communicator, write_pairs)

        if success:
            # 更新缓存
            self.lastcontrollersetting = DotDict(json.loads(json.dumps(controller_data._data)))
            print("所有Controller设置写入成功")
        else:
            print("Controller设置写入失败")

        return success
# 全局单例,运行时的设置
settings = Settings() 