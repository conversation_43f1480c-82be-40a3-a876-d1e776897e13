# controllerutil.py
# 数据处理部分
import struct
import time
import queue
import threading
from settings import settings
import math
import numpy as np
from Communicator import IControllerCommunicator
from Communicator import SerialCommunicator, TcpCommunicator, DataReaderThread

# --- 配置常量 ---
# 字节序 (根据实际情况修改)
ENDIANNESS = ">"  # 大端字节序 (">" for big-endian, "<" for little-endian)
CONFIG_FILENAME = "controller_config.json"

# --- 数据处理函数 ---
def process_received_data(endianness, received_data):
    """
    处理从串口接收到的原始数据
    
    参数:
        endianness: 字节序 (">" for big-endian, "<" for little-endian)
        received_data: 从串口读取的原始字节数据
        
    返回:
        处理后的u32_values列表，如果处理失败则返回None
    """
    try:
        num_u32_values = len(received_data) // 4
        if len(received_data) % 4 != 0:
            print("警告：接收到的数据长度不是4的倍数")
            return None
        
        u32_values = []
        for i in range(num_u32_values):
            start = i * 4
            end = start + 4
            try:
                u32_value = struct.unpack(f"{endianness}I", received_data[start:end])[0]
                if endianness == '>':  # 假设目标是小端字节序处理
                    swapped_words_value = swap_words(u32_value)
                    high_word = (swapped_words_value >> 16) & 0xFFFF
                    low_word = swapped_words_value & 0xFFFF
                    swapped_bytes_high = swap_bytes(high_word)
                    swapped_bytes_low = swap_bytes(low_word)
                    u32_value = (swapped_bytes_high << 16) | swapped_bytes_low
                u32_values.append(u32_value)
            except struct.error as e:
                print(f"struct解包错误，索引{i}: {e}")
                return None  # 错误时返回None
        
        return u32_values
    except Exception as e:
        print(f"处理接收数据时发生错误: {e}")
        return None


def reshape_data(u32_values, loop_index):
    """
    重塑和处理数据
    
    参数:
        u32_values: 处理过的u32值列表
        loop_index: 当前循环索引
        
    返回:
        处理后的数据列表和新的循环索引，如果处理失败则返回(None, loop_index)
    """
    try:
        data_len = len(u32_values)
        if data_len <= 3:
            print("数组长度太短(<4)，无法进行后续处理")
            return None, loop_index

        a = (data_len - 3) // 6
        if a <= 0:
            print(f"计算得到的行数a({a})无效，无法重塑数组")
            return None, loop_index
        a = a & 0xFFFFFFFF

        start_index = 3
        b = u32_values[start_index:]

        rows = a
        cols = 6
        total_elements = len(b)

        if rows * cols != total_elements:
            print(f"元素个数({total_elements})与维度({rows}x{cols})不匹配，无法重塑数组")
            return None, loop_index

        c = [b[i * cols:(i + 1) * cols] for i in range(rows)]
        f = []
        new_loop_index = loop_index
        
        for row_data in c:
            first_element = row_data[-1] if row_data else None
            row_after_delete = delete_first_element(row_data)

            if first_element is not None:
                high_word, low_word = split_number(first_element)
            else:
                high_word, low_word = None, None

            output_row = [
                # 武老师的程序中这里用的是时间戳，之后看看会不会有问题再来改
                new_loop_index,
                u32_values[0],
                u32_values[1],
                u32_values[2]
            ]
            new_loop_index += 1  # 每处理一行就增加索引
            
            if row_after_delete:
                output_row.extend(row_after_delete)
            # 反转高低字
            output_row.extend([low_word, high_word])
            # 处理数据，转换为实际的物理参数值
            processed_row = process_data(output_row)
            # 将处理后的数据添加到原始数据后面
            # output_row.extend(processed_row)
            
            f.append(processed_row)

        if not f:
            print("数据处理后结果为空，无法转置。")
            return None, new_loop_index

        transposed_f = list(zip(*f))
        
        return transposed_f, new_loop_index
    
    except Exception as e:
        print(f"重塑数据时发生错误: {e}")
        return None, loop_index

# --- 串口通信函数 ---
def dwrite(communicator: IControllerCommunicator, item, value):
    """
    模拟 LabVIEW 的 dWrite VI，将 item (U8) 和 value (U32) 写入通信器.
    Args:
        communicator: IControllerCommunicator 通信器对象
        item: U8 类型的 item 值
        value: U32 类型的 value 值
    """
    if isinstance(communicator, TcpCommunicator):
        try:
            communicator.send_command(item, value)
            return True
        except Exception as e:
            print(f"发送指令时发生错误: {e}")
            return False
    try:
        # 确保value是整数
        if isinstance(value, float):
            value = int(value)
            
        # 1. 构建字节数组 (根据设备协议调整)
        header = bytes([0xBB])
        item_byte = bytes([item])
        value_bytes = value.to_bytes(4, byteorder='little',signed=True) # 小端序，无符号
        tail = bytes([0x0D, 0x0A])
        message = header + item_byte + value_bytes + tail

        # 2. 写入控制器
        communicator.write_raw(message)
        # print(f"成功发送数据: item={item}, value={value} -> {message.hex()}")
        return True # 表示成功
    except IOError as e: # 捕获通用的I/O错误
        print(f"写入控制器失败 (item={item}, value={value}): {e}")
        return False
    except Exception as e:
        print(f"dwrite 发生意外错误 (item={item}, value={value}): {e}")
        return False # 表示失败

def multi_dwrite(communicator: IControllerCommunicator, data_pairs):
    """
    批量写入多个 (item, value) 对到串口.
    Args:
        communicator: IControllerCommunicator 通信器对象
        data_pairs: 一个列表，包含多个 (item, value) 元组。
    Returns:
        True 如果所有写入都成功，False 如果有任何一次写入失败。
    """
    all_success = True
    for item, value in data_pairs:
        if not dwrite(communicator, item, value):
            all_success = False
            # 可以选择在这里停止，或者继续尝试发送剩余指令
            print(f"写入失败: item={item}, value={value}")
            break # 如果希望遇到错误就停止，取消注释此行
    return all_success

def AOitem(register_in, AOChannel):
    """
    根据 AOChannel 枚举值，对 register_in (item, value) 对进行偏移。
    Args:
        register_in: (item, value) 元组，表示要写入的寄存器。
        AOChannel: 字符串，表示 AO 通道，可选值： "Bias", "Piezo", "RE", "CE".
    Returns:
        偏移后的 (item, value) 元组.
    Raises:
        ValueError: 如果 AOChannel 不是有效值。
    """
    item, value = register_in

    if AOChannel == "Bias":
        item_offset = 0
    elif AOChannel == "Piezo":
        item_offset = 16
    elif AOChannel == "RE":
        item_offset = 32
    elif AOChannel == "CE":
        item_offset = 48
    else:
        raise ValueError(f"Invalid AOChannel: {AOChannel}.  Valid values are: 'Bias', 'Piezo', 'RE', 'CE'.")

    new_item = item + item_offset
    return (new_item, value)

def load_config_and_initialize(communicator: IControllerCommunicator, config_filename=CONFIG_FILENAME, get_default_config_func=None):
    """
    使用settings对象中的配置初始化控制器，遍历所有的setting。
    Args:
        communicator: 通信器对象
        config_filename: 配置文件名（不再使用）
        get_default_config_func: 不再使用
    Returns:
        True 如果初始化成功，False 如果初始化失败。
    """
    # 从settings对象中提取初始化数据
    initialization_data = []
    
    try:
        controller = settings.data.Controller
        
        # 提取Motor配置
        if 'Motor' in controller._data:
            motor = controller.Motor
            for key, item_data in motor._data.items():
                if isinstance(item_data, dict) and 'item' in item_data and 'value' in item_data:
                    initialization_data.append([item_data['item'], item_data['value']])
        
        # 提取ADC配置
        if 'ADC' in controller._data:
            adc = controller.ADC
            for key, item_data in adc._data.items():
                if isinstance(item_data, dict) and 'item' in item_data and 'value' in item_data:
                    initialization_data.append([item_data['item'], item_data['value']])
        
        # 提取Heater配置
        if 'Heater' in controller._data:
            heater = controller.Heater
            for key, item_data in heater._data.items():
                if isinstance(item_data, dict) and 'item' in item_data and 'value' in item_data:
                    initialization_data.append([item_data['item'], item_data['value']])
        
        # 提取Relay配置
        if 'Relay' in controller._data:
            relay = controller.Relay
            for key, item_data in relay._data.items():
                if isinstance(item_data, dict) and 'item' in item_data and 'value' in item_data:
                    initialization_data.append([item_data['item'], item_data['value']])
        
        # 提取Vibrate配置
        if 'Vibrate' in controller._data:
            vibrate = controller.Vibrate
            for key, item_data in vibrate._data.items():
                if isinstance(item_data, dict) and 'item' in item_data and 'value' in item_data:
                    initialization_data.append([item_data['item'], item_data['value']])
        
        # 提取Bias配置
        if 'Bias' in controller._data and isinstance(controller.Bias, object) and hasattr(controller.Bias, '_data'):
            bias = controller.Bias
            for key, item_data in bias._data.items():
                if isinstance(item_data, dict) and 'item' in item_data and 'value' in item_data:
                    initialization_data.append([item_data['item'], item_data['value']])
        
        # 提取Piezo配置
        if 'Piezo' in controller._data and isinstance(controller.Piezo, object) and hasattr(controller.Piezo, '_data'):
            piezo = controller.Piezo
            for key, item_data in piezo._data.items():
                if isinstance(item_data, dict) and 'item' in item_data and 'value' in item_data:
                    initialization_data.append([item_data['item'], item_data['value']])
        
        # 提取RE配置
        if 'RE' in controller._data and isinstance(controller.RE, object) and hasattr(controller.RE, '_data'):
            re = controller.RE
            for key, item_data in re._data.items():
                if isinstance(item_data, dict) and 'item' in item_data and 'value' in item_data:
                    initialization_data.append([item_data['item'], item_data['value']])
        
        # 提取CE配置
        if 'CE' in controller._data and isinstance(controller.CE, object) and hasattr(controller.CE, '_data'):
            ce = controller.CE
            for key, item_data in ce._data.items():
                if isinstance(item_data, dict) and 'item' in item_data and 'value' in item_data:
                    initialization_data.append([item_data['item'], item_data['value']])
        
        # 提取REControl配置
        if 'REControl' in controller._data and isinstance(controller.REControl, object) and hasattr(controller.REControl, '_data'):
            recontrol = controller.REControl
            for key, item_data in recontrol._data.items():
                if isinstance(item_data, dict) and 'item' in item_data and 'value' in item_data:
                    initialization_data.append([item_data['item'], item_data['value']])
        
        if not initialization_data:
            print("没有找到有效的初始化数据")
            return False
            
        # 写入控制器
        print("开始初始化控制器...")
        if multi_dwrite(communicator, initialization_data):
            print("控制器初始化成功。")
            return True
        else:
            print("控制器初始化过程中发生错误。")
            return False
            
    except Exception as e:
        print(f"初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def AMP_State_to_value(RelayState):
    switch1, switch0 = 0,0
    if RelayState == "Open":
        switch1, switch0 = 0, 0
    elif RelayState == "Linear":
        switch1, switch0 = 0, 1
    elif RelayState == "Logarithmic":
        switch1, switch0 = 1, 0
    else:
        raise ValueError(f"Invalid value")
    return (switch0, switch1)

def AMP_value_to_State(switch0, switch1):
    """
    将放大器开关值转换为放大器状态枚举
    
    参数:
        switch0: 开关0的值 (0或1)
        switch1: 开关1的值 (0或1)
    
    返回:
        放大器状态枚举 ("Open", "Linear", "Logarithmic")
    """
    choose = switch1 * 2 + switch0
    
    if choose == 0:
        return "Open"
    elif choose == 1:
        return "Logarithmic"
    elif choose == 2:
        return "Linear"
    else:
        # 未定义状态 (两个开关都为1)
        return "Unknown"

def AmpLog(voltage_array, logarithmic_settings):
    """
    对输入电压应用对数放大器转换
    
    参数:
        voltage_array: 输入电压数组
        logarithmic_settings: 对数放大器设置对象，包含a0, b0, a1, b1参数
    
    返回:
        转换后的电流数组
    """
    # 为输入创建深拷贝，避免修改原始数组
    processed_values = np.array(voltage_array, dtype=float)
    # 创建符号数组，记录每个值的正负性
    negative = np.ones(len(voltage_array))
    
    # 对每个值应用对数转换
    for i in range(len(processed_values)):
        voltage = processed_values[i]
        
        if voltage > 0:
            processed_values[i] = voltage * logarithmic_settings.a0 + logarithmic_settings.b0
            negative[i] = 1
        else:
            processed_values[i] = voltage * logarithmic_settings.a1 + logarithmic_settings.b1
            negative[i] = -1
    
    # 对变换后的值计算10的幂次
    current_array = np.power(10, processed_values)
    
    # 恢复正负号
    current_array = current_array * negative
    
    return current_array

def AmpLinear(voltage_array, linear_settings):
    """
    对输入电压应用线性放大器转换
    
    参数:
        voltage_array: 输入电压数组
        linear_settings: 线性放大器设置对象，包含a和b参数
    
    返回:
        转换后的电流数组
    """
    # 为输入创建深拷贝，避免修改原始数组
    processed_values = np.array(voltage_array, dtype=float)
    
    # 对每个值应用线性转换: Current = (Voltage - b) / a
    current_array = (processed_values - linear_settings.b) / linear_settings.a
    
    return current_array

def calculate_current_and_conductance(settings, ai0_voltage, bias_voltage):
    """
    根据放大器状态计算电流和电导率
    
    参数:
        settings: 控制器设置对象
        ai0_voltage: AI0电压值
        bias_voltage: 偏置电压值
    
    返回:
        (current_mA, conductance_S, log_conductance)
        - current_mA: 电流值(mA)
        - conductance_S: 电导率(S)
        - log_conductance: Log(G/G0)值
    """
    try:
        # 获取放大器状态
        switch0 = settings.data.Controller.Relay.Switch0.value
        switch1 = settings.data.Controller.Relay.Switch1.value
        state = AMP_value_to_State(switch0, switch1)
        
        # 根据放大器状态计算电流
        if state == "Linear":
            # 线性放大器模式
            a = settings.data.Amplifier.Linear.a
            b = settings.data.Amplifier.Linear.b
            linear_settings = type('LinearSettings', (), {'a': a, 'b': b})
            current_A = AmpLinear([ai0_voltage], linear_settings)[0]
        elif state == "Logarithmic":
            # 对数放大器模式
            a0 = settings.data.Amplifier.Logarithmic.a0
            b0 = settings.data.Amplifier.Logarithmic.b0
            a1 = settings.data.Amplifier.Logarithmic.a1
            b1 = settings.data.Amplifier.Logarithmic.b1
            logarithmic_settings = type('LogarithmicSettings', (), {'a0': a0, 'b0': b0, 'a1': a1, 'b1': b1})
            current_A = AmpLog([ai0_voltage], logarithmic_settings)[0]
        else:
            # 开路或未知状态
            current_A = ai0_voltage * 1e-9  # 默认转换因子
    except AttributeError as e:
        # print(f"计算电流时出错: {e}")
        current_A = ai0_voltage * 1e-9  # 默认转换因子
        
    # 转换为mA
    current_mA = current_A * 1e3
    # 计算电导率(S)
    G0 = 7.748e-5  # 量子电导单位(S)
    
    # 防止除以零
    if abs(bias_voltage) < 1e-12:
        conductance_S = 0
    else:
        conductance_S = abs(current_A / bias_voltage)
    
    # 计算电导率的量子单位表示 (G/G0)
    conductance_G0 = conductance_S / G0
    
    # 计算Log(G/G0)
    # 防止取对数时出错
    if conductance_G0 <= 0:
        log_conductance = -9  # 设定一个较小的默认值
    else:
        log_conductance = math.log10(conductance_G0)
    
    return current_mA, conductance_S, log_conductance

# --- 类型转换函数 ---
def uint32_to_int32(value):
    """将无符号32位整数转换为有符号32位整数"""
    if value is None:
        return 0
    # 使用Python的int.from_bytes方法处理符号转换
    return int.from_bytes(value.to_bytes(4, byteorder='little', signed=False), 
                         byteorder='little', signed=True)

def uint16_to_int16(value):
    """将无符号16位整数转换为有符号16位整数"""
    if value is None:
        return 0
    value = value & 0xFFFF  # 确保只取低16位
    return int.from_bytes(value.to_bytes(2, byteorder='little', signed=False), 
                         byteorder='little', signed=True)

def to_temperature(value):
    """将原始值转换为温度值（乘以0.1）"""
    return float(uint32_to_int32(value)) * 0.1 if value is not None else 0

def to_voltage(value):
    """将原始值转换为电压值（乘以1e-6）"""
    return float(uint32_to_int32(value)) * 1e-6 if value is not None else 0

def to_scaled_analog(value):
    """将原始16位值转换为标度模拟量（除以3276.8）"""
    return float(uint16_to_int16(value)) / 3276.8 if value is not None else 0

def calculate_conductance(current, bias):
    """
    计算电导率 G/G0
    
    参数:
        current: 电流值 (A)
        bias: 偏置电压 (V)
    
    返回:
        电导率 G/G0 (无单位)
    """
    # 量子电导单位 G0 = 2e^2/h ≈ 7.748e-5 S
    G0 = 7.748e-5  # 西门子 (S)
    
    # 防止除以零
    if abs(bias) < 1e-12:
        return 0
    
    # 计算电导 G = I/V
    conductance = current / bias
    
    # 转换为 G/G0
    conductance_G0 = conductance / G0
    
    return conductance_G0

# --- 辅助函数 ---
def swap_words(value):
    """交换一个 32 位整数的高 16 位和低 16 位."""
    high_word = (value >> 16) & 0xFFFF
    low_word = value & 0xFFFF
    swapped_value = (low_word << 16) | high_word
    return swapped_value

def swap_bytes(value):
    """交换一个 16 位整数的高 8 位和低 8 位."""
    high_byte = (value >> 8) & 0xFF
    low_byte = value & 0xFF
    swapped_value = (low_byte << 8) | high_byte
    return swapped_value

def delete_first_element(arr):
    """删除数组的最后一个元素（模拟LabVIEW的Delete From Array函数）"""
    if len(arr) > 0:
        return arr[:-1]
    else:
        return []

def split_number(num):
    """拆分32位整数为高16位和低16位（模拟LabVIEW的Split Number函数）"""
    num = num & 0xFFFFFFFF  # 确保num是32位无符号整数
    high_word = (num >> 16) & 0xFFFF
    low_word = num & 0xFFFF
    return high_word, low_word

def process_data(raw_data_array):
    """
    处理原始数据数组，转换为实际的物理参数值
    
    参数:
        raw_data_array: 原始数据数组，包含以下元素:
            [ID, PWMCounter, TS0, TS1, Bias, Piezo, AO2, AO3, dT, AI0, AI1]
    
    返回:
        处理后的数据数组，包含以下物理量:
            [ID, MotorPosition, DUT Temperature, Ambient Temperature, 
             Bias, Piezo, RE, CE, dT(uSec), AI0, AI1, 
             Log(G/G0), Current, MotorPosition, PiezoPosition]
    """
    if not raw_data_array or len(raw_data_array) < 11:
        print("原始数据数组长度不足，无法处理")
        return []
    
    processed_data = []
    
    # ID: 原样保留
    processed_data.append(raw_data_array[0])
    
    # MotorPosition: PWMCounter(int32)
    motor_position = uint32_to_int32(raw_data_array[1])
    processed_data.append(motor_position)
    
    # DUT Temperature: TS0(->int32->double)*0.1
    dut_temp = to_temperature(raw_data_array[2])
    processed_data.append(dut_temp)
    
    # Ambient Temperature: TS1(->int32->double)*0.1
    amb_temp = to_temperature(raw_data_array[3])
    processed_data.append(amb_temp)
    
    # Bias: Bias(->int32->double)*(1e-6) [V]
    bias_voltage = to_voltage(raw_data_array[4])
    processed_data.append(bias_voltage)
    
    # Piezo: Piezo(->int32->double)*(1e-6) [V]
    piezo_voltage = to_voltage(raw_data_array[5])
    processed_data.append(piezo_voltage)
    
    # RE: AO2(->int32->double)*(1e-6)
    re_voltage = to_voltage(raw_data_array[6])
    processed_data.append(re_voltage)
    
    # CE: AO3(->int32->double)*(1e-6)
    ce_voltage = to_voltage(raw_data_array[7])
    processed_data.append(ce_voltage)
    
    # dT(uSec): dT(u32) - 通常是无符号值
    dt = int(raw_data_array[8]) if raw_data_array[8] is not None else 0
    processed_data.append(dt)
    
    # AI0: AI0(->int16->double)/3276.8 [V]
    ai0_voltage = to_scaled_analog(raw_data_array[9])
    processed_data.append(ai0_voltage)
    
    # AI1: AI1(->int16->double)/3276.8 [V]
    ai1_voltage = to_scaled_analog(raw_data_array[10])
    processed_data.append(ai1_voltage)
    
    # 计算电流和电导率
    current_mA, conductance_S, log_conductance = calculate_current_and_conductance(
        settings, ai0_voltage, bias_voltage
    )
    
    # 转换为A单位以保持与原代码一致
    current_A = current_mA / 1e3
    
    # 添加计算的值
    processed_data.append(log_conductance)  # Log(G/G0)
    processed_data.append(current_A)        # Current (A)
    
    # 计算物理位置
    try:
        # MotorPosition (物理单位)
        motor_pitch = settings.data.Motor.Pitch if hasattr(settings.data, 'Motor') else 0
        motor_pos_physical = motor_position * motor_pitch if motor_pitch != 0 else 0
        processed_data.append(motor_pos_physical)
        
        # PiezoPosition (物理单位)
        piezo_pitch = settings.data.Controller.Flow.Piezo.Pitch if 'Flow' in settings.data.Controller._data else 0
        piezo_pos_physical = piezo_voltage * piezo_pitch if piezo_pitch != 0 else 0
        processed_data.append(piezo_pos_physical)
    except AttributeError as e:
        # 如果无法访问属性，添加默认值，但输出错误
        # print(f"计算物理位置时出错: {e}")
        processed_data.append(0)  # MotorPosition default
        processed_data.append(0)  # PiezoPosition default
    
    return processed_data




