<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>串口读取控制器项目 - 详细架构说明</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
            margin-bottom: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            margin: -20px -20px 40px -20px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #667eea;
        }
        
        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 10px;
        }
        
        .section h3 {
            color: #34495e;
            margin: 20px 0 15px 0;
            font-size: 1.4em;
        }
        
        .section h4 {
            color: #7f8c8d;
            margin: 15px 0 10px 0;
            font-size: 1.2em;
        }
        
        .architecture-diagram {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .layer {
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            position: relative;
        }
        
        .layer:nth-child(1) { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
        .layer:nth-child(2) { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
        .layer:nth-child(3) { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }
        .layer:nth-child(4) { background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%); }
        
        .layer h3 {
            margin-bottom: 15px;
            color: #2c3e50;
            font-size: 1.3em;
        }
        
        .layer-components {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }
        
        .component {
            background: rgba(255, 255, 255, 0.8);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
            color: #2c3e50;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .module-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .module-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #3498db;
        }
        
        .module-card h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .module-card .file-name {
            background: #ecf0f1;
            padding: 5px 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            color: #e74c3c;
            margin-bottom: 10px;
            display: inline-block;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .data-flow {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .flow-step {
            flex: 1;
            text-align: center;
            padding: 10px;
            background: #3498db;
            color: white;
            border-radius: 5px;
            margin: 0 5px;
            font-size: 0.9em;
        }
        
        .arrow {
            font-size: 1.5em;
            color: #3498db;
            margin: 0 10px;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }
        
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        
        .tech-item {
            background: #3498db;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }
        
        ul, ol {
            margin-left: 20px;
            margin-bottom: 15px;
        }
        
        li {
            margin-bottom: 8px;
        }
        
        .pattern-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            border-left: 4px solid #e74c3c;
        }
        
        .pattern-card h4 {
            color: #e74c3c;
            margin-bottom: 10px;
        }
        
        .state-diagram {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .state-box {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 2px solid #3498db;
            position: relative;
        }
        
        .state-box.active {
            background: #3498db;
            color: white;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .module-grid {
                grid-template-columns: 1fr;
            }
            
            .data-flow {
                flex-direction: column;
            }
            
            .flow-step {
                margin: 5px 0;
            }
            
            .arrow {
                transform: rotate(90deg);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏗️ 串口读取控制器项目详细架构</h1>
            <p>基于PyQt6的高精度设备控制与数据采集系统 - 完整技术架构解析</p>
        </div>
        
        <div class="section">
            <h2>🎯 项目概述</h2>
            <p>这是一个专业的<span class="highlight">精密仪器自动化控制系统</span>，采用现代软件工程设计模式，实现了硬件设备的精确控制、实时数据采集、智能状态管理和可视化显示。系统支持串口和TCP/IP双重通信协议，具备高度的可扩展性和可维护性。</p>
            
            <div class="tech-stack">
                <span class="tech-item">Python 3.12</span>
                <span class="tech-item">PyQt6</span>
                <span class="tech-item">PyVISA</span>
                <span class="tech-item">PyQtGraph</span>
                <span class="tech-item">NumPy</span>
                <span class="tech-item">Threading</span>
                <span class="tech-item">State Machine</span>
                <span class="tech-item">Abstract Factory</span>
            </div>
        </div>

        <div class="section">
            <h2>🏛️ 系统架构层次</h2>
            <div class="architecture-diagram">
                <div class="layer">
                    <h3>🖥️ 表示层 (Presentation Layer)</h3>
                    <div class="layer-components">
                        <span class="component">PyQt6主窗口</span>
                        <span class="component">数据表格显示</span>
                        <span class="component">实时图表绘制</span>
                        <span class="component">设置对话框</span>
                        <span class="component">用户交互控件</span>
                    </div>
                </div>
                
                <div class="layer">
                    <h3>🧠 业务逻辑层 (Business Logic Layer)</h3>
                    <div class="layer-components">
                        <span class="component">状态机控制</span>
                        <span class="component">数据处理算法</span>
                        <span class="component">设备控制逻辑</span>
                        <span class="component">配置管理</span>
                        <span class="component">事件处理</span>
                    </div>
                </div>
                
                <div class="layer">
                    <h3>📡 通信抽象层 (Communication Layer)</h3>
                    <div class="layer-components">
                        <span class="component">抽象通信接口</span>
                        <span class="component">串口通信器</span>
                        <span class="component">TCP通信器</span>
                        <span class="component">虚拟通信器</span>
                        <span class="component">数据读取线程</span>
                    </div>
                </div>
                
                <div class="layer">
                    <h3>⚙️ 硬件抽象层 (Hardware Layer)</h3>
                    <div class="layer-components">
                        <span class="component">设备控制接口</span>
                        <span class="component">寄存器操作</span>
                        <span class="component">物理量转换</span>
                        <span class="component">硬件初始化</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📁 核心模块详解</h2>
            <div class="module-grid">
                <div class="module-card">
                    <h4>🖥️ 主界面模块</h4>
                    <div class="file-name">serial_monitor.py</div>
                    <p><strong>职责：</strong>系统入口点，负责UI界面管理和用户交互</p>
                    <ul>
                        <li>PyQt6主窗口初始化</li>
                        <li>数据表格显示和更新</li>
                        <li>用户控制按钮事件处理</li>
                        <li>实时图表集成</li>
                        <li>设置对话框调用</li>
                    </ul>
                    <div class="code-block">
class SerialMonitor(QMainWindow):
    def __init__(self):
        self.communicator = SerialCommunicator()
        self.mode_state_machine = ModeStateMachine()
        self.init_ui()
                    </div>
                </div>

                <div class="module-card">
                    <h4>📡 通信抽象层</h4>
                    <div class="file-name">Communicator.py</div>
                    <p><strong>职责：</strong>实现通信协议的抽象和具体实现</p>
                    <ul>
                        <li>IControllerCommunicator抽象接口</li>
                        <li>SerialCommunicator串口实现</li>
                        <li>TcpCommunicator网络实现</li>
                        <li>VirtualCommunicator虚拟实现</li>
                        <li>DataReaderThread数据读取线程</li>
                    </ul>
                    <div class="code-block">
class IControllerCommunicator(ABC):
    @abstractmethod
    def connect(self, config_params): pass
    @abstractmethod
    def read_bytes(self, num_bytes): pass
                    </div>
                </div>

                <div class="module-card">
                    <h4>🎛️ 硬件控制层</h4>
                    <div class="file-name">HDControll.py</div>
                    <p><strong>职责：</strong>硬件设备的高级控制接口</p>
                    <ul>
                        <li>电机位置控制</li>
                        <li>压电器件电压设置</li>
                        <li>加热器温度控制</li>
                        <li>继电器开关控制</li>
                        <li>设备初始化和配置</li>
                    </ul>
                    <div class="code-block">
def motor_move_to_position(target_position):
    if _check_communicator():
        dwrite(_communicator, AOitem.MotorTargetStep,
               target_position)
                    </div>
                </div>

                <div class="module-card">
                    <h4>🤖 状态机控制</h4>
                    <div class="file-name">StateMachine.py</div>
                    <p><strong>职责：</strong>智能状态管理和自动化控制逻辑</p>
                    <ul>
                        <li>MotorMode枚举定义</li>
                        <li>ModeStateMachine状态机</li>
                        <li>电导阈值监控</li>
                        <li>自动扫描控制</li>
                        <li>状态转换逻辑</li>
                    </ul>
                    <div class="code-block">
class ModeStateMachine:
    def receive_data_value(self, piezo, logg):
        # 根据电导值和压电值驱动状态转换
        if self._current_mode == MotorMode.SCAN:
            self._handle_scan_mode(piezo, logg)
                    </div>
                </div>

                <div class="module-card">
                    <h4>🔧 控制器工具</h4>
                    <div class="file-name">controllerutil.py</div>
                    <p><strong>职责：</strong>底层数据处理和设备操作工具</p>
                    <ul>
                        <li>原始数据解析和转换</li>
                        <li>物理量计算</li>
                        <li>寄存器读写操作</li>
                        <li>数据格式转换</li>
                        <li>设备配置加载</li>
                    </ul>
                    <div class="code-block">
def process_received_data(endianness, raw_data):
    u32_values = []
    for i in range(len(raw_data) // 4):
        value = struct.unpack(f"{endianness}I",
                            raw_data[i*4:(i+1)*4])[0]
        u32_values.append(value)
                    </div>
                </div>

                <div class="module-card">
                    <h4>📊 数据可视化</h4>
                    <div class="file-name">plotgraph.py</div>
                    <p><strong>职责：</strong>实时数据图表绘制和显示</p>
                    <ul>
                        <li>LogGPlot实时绘图类</li>
                        <li>滚动窗口数据显示</li>
                        <li>双曲线绘制(Log(G/G0)和Piezo)</li>
                        <li>自动缩放和网格显示</li>
                        <li>高性能数据更新</li>
                    </ul>
                    <div class="code-block">
class LogGPlot(QWidget):
    def update_data(self, new_logg, new_piezo):
        self.data[self.ptr % self.window_size] = new_logg
        self.piezo_data[self.ptr % self.window_size] = new_piezo
        self.ptr += 1
                    </div>
                </div>

                <div class="module-card">
                    <h4>⚙️ 配置管理</h4>
                    <div class="file-name">settings.py & settings.json</div>
                    <p><strong>职责：</strong>系统配置的加载、保存和管理</p>
                    <ul>
                        <li>DotDict嵌套配置访问</li>
                        <li>JSON配置文件解析</li>
                        <li>设备参数管理</li>
                        <li>运行时配置修改</li>
                        <li>配置验证和默认值</li>
                    </ul>
                    <div class="code-block">
class Settings:
    def get_value(self, path):
        parts = path.split('.')
        current = self.data
        for part in parts:
            current = getattr(current, part)
        return current
                    </div>
                </div>

                <div class="module-card">
                    <h4>🔧 设置对话框</h4>
                    <div class="file-name">settings_dialog.py</div>
                    <p><strong>职责：</strong>用户友好的配置界面</p>
                    <ul>
                        <li>分类配置界面</li>
                        <li>实时参数验证</li>
                        <li>配置预览和应用</li>
                        <li>设备连接测试</li>
                        <li>配置导入导出</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔄 数据流程详解</h2>
            <div class="data-flow">
                <div class="flow-step">硬件设备<br>原始数据</div>
                <div class="arrow">→</div>
                <div class="flow-step">通信层<br>字节读取</div>
                <div class="arrow">→</div>
                <div class="flow-step">数据解析<br>u32转换</div>
                <div class="arrow">→</div>
                <div class="flow-step">物理量<br>计算转换</div>
                <div class="arrow">→</div>
                <div class="flow-step">状态机<br>逻辑处理</div>
                <div class="arrow">→</div>
                <div class="flow-step">界面更新<br>图表显示</div>
            </div>

            <h3>📋 详细数据处理步骤：</h3>
            <ol>
                <li><strong>原始数据采集</strong>：DataReaderThread从硬件读取1230*4字节的原始数据包</li>
                <li><strong>字节序转换</strong>：按大端序(Big-Endian)解析字节流为u32数值数组</li>
                <li><strong>数据重塑</strong>：将一维数据重塑为123通道×10采样点的二维数组</li>
                <li><strong>物理量转换</strong>：通过放大器参数将原始ADC值转换为电压、电流、温度等物理量</li>
                <li><strong>关键参数提取</strong>：计算Log(G/G0)电导值和Piezo压电电压</li>
                <li><strong>状态机驱动</strong>：将关键参数传递给状态机进行逻辑判断</li>
                <li><strong>界面更新</strong>：通过Qt信号槽机制更新表格显示和实时图表</li>
            </ol>

            <h3>🔢 关键物理量计算：</h3>
            <div class="code-block">
# Log(G/G0)电导计算
logg_value = math.log10(abs(current_value / reference_current))

# Piezo电压转换 (控制器值转物理值)
piezo_voltage = controller_value / 1000000.0  # 微伏转伏特

# 温度计算 (热敏电阻)
temperature = calculate_temperature_from_resistance(resistance_value)

# 电机位置转换
motor_position_mm = motor_steps * step_size_mm
            </div>
        </div>

        <div class="section">
            <h2>🎨 核心设计模式</h2>

            <div class="pattern-card">
                <h4>🏭 抽象工厂模式 (Abstract Factory)</h4>
                <p><strong>应用场景：</strong>通信层设计，支持多种通信协议</p>
                <div class="code-block">
class IControllerCommunicator(ABC):
    """抽象通信器接口"""
    @abstractmethod
    def connect(self, config_params): pass
    @abstractmethod
    def read_bytes(self, num_bytes): pass

class SerialCommunicator(IControllerCommunicator):
    """串口通信具体实现"""

class TcpCommunicator(IControllerCommunicator):
    """TCP通信具体实现"""
                </div>
                <p><strong>优势：</strong>易于扩展新的通信协议，代码解耦，便于测试</p>
            </div>

            <div class="pattern-card">
                <h4>🔄 状态模式 (State Pattern)</h4>
                <p><strong>应用场景：</strong>电机控制的智能状态管理</p>
                <div class="code-block">
class MotorMode(Enum):
    IDLE = "idle"           # 空闲状态
    SCAN = "scan"           # 扫描状态
    APPROACH = "approach"   # 接近状态
    CONTACT = "contact"     # 接触状态

class ModeStateMachine:
    def __init__(self):
        self._current_mode = MotorMode.IDLE
        self._current_modestate = MotorFlowStep.IDLE
                </div>
                <p><strong>优势：</strong>状态转换逻辑清晰，易于维护和扩展</p>
            </div>

            <div class="pattern-card">
                <h4>🎯 单例模式 (Singleton)</h4>
                <p><strong>应用场景：</strong>通信器实例和配置管理</p>
                <div class="code-block">
class IControllerCommunicator(ABC):
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super().__new__(cls)
        return cls._instance
                </div>
                <p><strong>优势：</strong>确保全局唯一实例，避免资源冲突</p>
            </div>

            <div class="pattern-card">
                <h4>👁️ 观察者模式 (Observer)</h4>
                <p><strong>应用场景：</strong>Qt信号槽机制实现数据更新通知</p>
                <div class="code-block">
class SerialSignals(QObject):
    data_ready = pyqtSignal(list)
    no_data = pyqtSignal()
    error_occurred = pyqtSignal(str)

# 数据读取线程发送信号
self.signals.data_ready.emit(processed_data)

# 主界面接收信号
self.data_reader_thread.signals.data_ready.connect(
    self.update_data_display)
                </div>
                <p><strong>优势：</strong>松耦合的事件通知机制，支持多观察者</p>
            </div>
        </div>
