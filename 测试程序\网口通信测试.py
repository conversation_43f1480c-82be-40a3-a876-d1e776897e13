import sys
import socket
import struct
import threading
from PyQt6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLineEdit, QTextBrowser, QLabel, QGroupBox, QScrollBar,
    QCheckBox, QFileDialog, QGridLayout, QComboBox, QSpinBox
)
from PyQt6.QtCore import pyqtSignal, QObject, QThread, Qt, QTimer
import time
import os
from datetime import datetime
import pyqtgraph as pg
import numpy as np

# --- 常量定义 ---
# 默认的TCP连接参数
DEFAULT_TCP_IP = '************'
DEFAULT_TCP_PORT = 5001
# 接收缓冲区大小 - 增大以应对高速数据流
BUFFER_SIZE = 262144  # 256KB缓冲区，原来是65536(64KB)
# GUI更新频率控制 - 避免界面卡顿
GUI_UPDATE_INTERVAL_MS = 200  # 从50ms增加到200ms
PARSED_DATA_UPDATE_INTERVAL_MS = 500  # 从100ms增加到500ms

# 数据帧结构固定偏移量和长度定义
HEADER_BYTE = 0xBB  # 包头字节
FRAME_ID_OFFSET = 1  # 帧ID的偏移量
FRAME_ID_LEN = 4     # 帧ID的长度 (uint32_t)
ARM_ID_OFFSET = FRAME_ID_OFFSET + FRAME_ID_LEN  # ARM ID的偏移量
ARM_ID_LEN = 12      # ARM ID的长度 (3 x uint32_t)
HOVER_RESULT_OFFSET = ARM_ID_OFFSET + ARM_ID_LEN  # 悬停结果的偏移量
HOVER_RESULT_LEN = 4 # 悬停结果的长度 (uint32_t)
HOVER_COUNT_OFFSET = HOVER_RESULT_OFFSET + HOVER_RESULT_LEN  # 悬停计数值的偏移量
HOVER_COUNT_LEN = 4  # 悬停计数值的长度 (uint32_t)
NUM_GROUPS_OFFSET = HOVER_COUNT_OFFSET + HOVER_COUNT_LEN  # 数据组数n的偏移量
NUM_GROUPS_LEN = 4   # 数据组数n的长度 (uint32_t)

# 主数据头部的总长度（从0xBB开始算起，到数据组数n结束）
MAIN_HEADER_TOTAL_LEN = 1 + FRAME_ID_LEN + ARM_ID_LEN + HOVER_RESULT_LEN + HOVER_COUNT_LEN + NUM_GROUPS_LEN

# 每组数据的长度
DATA_GROUP_LEN = 32

# 包尾字节
FOOTER_BYTES = b'\x0D\x0A'
FOOTER_LEN = 2


# --- 工作线程信号定义 ---
# 用于TCP客户端工作线程与GUI主线程通信的信号
class WorkerSignals(QObject):
    connected = pyqtSignal()  # 连接成功信号
    disconnected = pyqtSignal() # 断开连接信号
    error = pyqtSignal(str)   # 错误信息信号
    raw_data_received = pyqtSignal(bytes) # 接收到原始数据信号
    parsed_data = pyqtSignal(dict) # 解析完成数据信号 (发送字典形式的数据)
    statistics_updated = pyqtSignal(dict) # 统计信息更新信号
    command_confirmed = pyqtSignal(int, int) # 指令确认信号 (指令序号, 指令值)

# --- 发送线程信号定义 ---
class SenderSignals(QObject):
    command_sent = pyqtSignal(int, int)  # 指令发送信号 (指令序号, 指令值)
    command_confirmed = pyqtSignal(int, int)  # 指令确认信号 (指令序号, 指令值)
    command_timeout = pyqtSignal(int, int)  # 指令超时信号 (指令序号, 指令值)
    send_progress = pyqtSignal(int, int)  # 发送进度信号 (当前, 总数)
    send_completed = pyqtSignal()  # 发送完成信号
    send_error = pyqtSignal(str)  # 发送错误信号


# --- TCP客户端工作线程 ---
# 负责TCP连接、数据接收和初步帧处理，在独立的线程中运行以避免阻塞GUI
class TcpClientWorker(QThread):
    def __init__(self, ip, port):
        super().__init__()
        self.ip = ip
        self.port = port
        self.signals = WorkerSignals() # 实例化信号对象
        self.is_running = True        # 线程运行标志
        self.socket = None            # 套接字对象
        self.receive_buffer = b''     # 接收缓冲区，用于处理不完整的帧

        # 统计信息
        self.frame_count = 0          # 接收到的帧数
        self.data_group_count = 0     # 接收到的数据组总数
        self.bytes_received = 0       # 接收到的字节总数
        self.start_time = None        # 开始时间
        self.last_gui_update = 0      # 上次GUI更新时间
        self.last_parsed_update = 0   # 上次解析数据更新时间
        self.latest_frame_data = None # 最新的帧数据（用于GUI显示）

        # 数据记录相关
        self.data_logging_enabled = False  # 数据记录开关
        self.log_file_path = None         # 日志文件路径
        self.log_file = None              # 日志文件对象

        # 解析控制参数
        self.max_groups_to_parse = 10     # 默认解析前10组数据

        # 波形数据缓存 - 示波器模式
        self.waveform_data = {}           # 存储各通道的波形数据 {channel_name: [values]}
        self.waveform_x_index = 0         # 当前写入位置索引
        self.waveform_buffer_size = 100000 # 固定缓冲区大小（x轴范围 0-100000）
        self.waveform_valid_data_count = 0 # 有效数据点数量
        self.waveform_has_wrapped = False  # 是否已经循环过一次

        # 初始化波形缓冲区
        self.init_waveform_buffers()

    def run(self):
        """线程主循环：连接TCP并持续接收数据"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(1.0) # 设置套接字超时时间，防止阻塞
            self.socket.connect((self.ip, self.port))
            self.start_time = time.time() # 记录开始时间
            self.signals.connected.emit() # 发送连接成功信号

            while self.is_running:
                try:
                    data = self.socket.recv(BUFFER_SIZE)
                    if not data:
                        # 如果recv返回空字节，表示服务器已关闭连接
                        self.signals.error.emit("服务器已断开连接。")
                        break

                    # 统计接收的字节数
                    self.bytes_received += len(data)

                    # 在添加到缓冲区之前，先检测和移除回显帧
                    filtered_data = self.filter_echo_frames(data)

                    # 只有过滤后还有数据才处理
                    if len(filtered_data) > 0:
                        self.receive_buffer += filtered_data # 将过滤后的数据添加到缓冲区
                        self.process_buffer()       # 处理缓冲区中的数据，尝试解析帧
                except socket.timeout:
                    continue # 超时无数据，继续循环等待
                except OSError as e:
                    # 捕获套接字操作系统的错误
                    if self.is_running:
                        self.signals.error.emit(f"套接字错误: {e}")
                    break
                except Exception as e:
                    # 捕获其他未知错误
                    if self.is_running:
                        self.signals.error.emit(f"接收数据时发生未知错误: {e}")
                    break
        except ConnectionRefusedError:
            self.signals.error.emit("连接被拒绝。请检查下位机是否运行或IP/端口是否正确。")
        except socket.gaierror:
            self.signals.error.emit("无效的IP地址或主机名。")
        except Exception as e:
            self.signals.error.emit(f"连接失败: {e}")
        finally:
            self.stop_client() # 无论成功失败，确保客户端停止

    def filter_echo_frames(self, data):
        """
        从接收到的数据中检测和移除指令回显帧
        Args:
            data (bytes): 接收到的原始数据
        Returns:
            bytes: 移除回显帧后的数据
        """
        if len(data) == 0:
            return data

        filtered_data = bytearray()
        i = 0

        while i < len(data):
            # 查找帧头 0xBB
            if data[i] == 0xBB:
                # 检查是否有足够的数据构成指令帧 (8字节)
                if 8 <= len(data):

                    potential_echo = data[i:i+8]

                    # 检查是否符合指令回显帧格式: BB + u8 + u32 + \r\n
                    if potential_echo[-2:] == b'\r\n':
                        # 这是一个指令回显帧
                        command_num = potential_echo[1]
                        command_value = struct.unpack('<I', potential_echo[2:6])[0]

                        hex_content = ' '.join(f'{byte:02X}' for byte in potential_echo)
                        print(f"✅ 实时检测到指令回显帧: {hex_content}")
                        print(f"✅ 指令确认 - 序号:{command_num}, 值:{command_value}")

                        # 发送指令确认信号到GUI
                        self.signals.command_confirmed.emit(command_num, command_value)

                        # 跳过这8个字节（不添加到过滤后的数据中）
                        i += 8
                        continue
                    else:
                        # 不是指令回显帧，保留这个字节
                        filtered_data.append(data[i])
                        i += 1
                else:
                    # 数据不足7字节，保留剩余所有数据
                    filtered_data.extend(data[i:])
                    break
            else:
                # 不是帧头，保留这个字节
                filtered_data.append(data[i])
                i += 1

        return bytes(filtered_data)

    def process_buffer(self):
        """处理接收缓冲区，尝试识别和解析完整的帧"""
        processed_frames = 0
        max_frames_per_batch = 10  # 每批最多处理10帧，避免长时间阻塞
        
        while processed_frames < max_frames_per_batch:
            # 查找帧头字节
            header_index = self.receive_buffer.find(bytes([HEADER_BYTE]))
            if header_index == -1:
                # 如果缓冲区过大且没有帧头，则清空缓冲区
                if len(self.receive_buffer) > BUFFER_SIZE * 2:
                    self.receive_buffer = b''
                break
            
            # 移除帧头之前的无效数据
            if header_index > 0:
                discarded_bytes = header_index
                self.receive_buffer = self.receive_buffer[header_index:]
                header_index = 0 # 帧头现在在缓冲区的起始位置

            # 回显帧已经在filter_echo_frames中处理，这里不需要再检测

            # 检查是否有足够的数据用于解析主头部（固定部分）
            if len(self.receive_buffer) < MAIN_HEADER_TOTAL_LEN:
                break # 数据不足，等待更多

            # 提取 num_groups 以确定完整帧的预期长度
            try:
                # num_groups 位于 MAIN_HEADER_TOTAL_LEN - NUM_GROUPS_LEN 处
                num_groups_data = self.receive_buffer[NUM_GROUPS_OFFSET : NUM_GROUPS_OFFSET + NUM_GROUPS_LEN]
                num_groups = struct.unpack('<I', num_groups_data)[0]
            except struct.error as e:
                # 如果主头部格式不正确，则丢弃当前帧头，继续查找下一个
                self.receive_buffer = self.receive_buffer[1:]
                continue

            # 验证数据组数的合理性
            if num_groups > 1000:
                print(f"WARNING: 数据组数异常({num_groups})，可能数据损坏，丢弃当前帧头")
                self.receive_buffer = self.receive_buffer[1:]
                continue

            # 计算完整帧的预期总长度
            expected_frame_len = MAIN_HEADER_TOTAL_LEN + (num_groups * DATA_GROUP_LEN) + FOOTER_LEN

            # 检查缓冲区是否有足够的数据构成一个完整的帧
            if len(self.receive_buffer) < expected_frame_len:
                break # 数据不足，等待更多

            # 提取一个潜在的完整帧
            full_frame_data = self.receive_buffer[:expected_frame_len]

            # 检查帧尾是否匹配
            if full_frame_data[-FOOTER_LEN:] != FOOTER_BYTES:
                # 帧尾不匹配，可能帧已损坏或帧头出现在数据载荷中
                # 丢弃当前帧头，从下一个字节开始重新查找帧头
                actual_footer = ' '.join(f'{byte:02X}' for byte in full_frame_data[-FOOTER_LEN:])
                expected_footer = ' '.join(f'{byte:02X}' for byte in FOOTER_BYTES)
                self.receive_buffer = self.receive_buffer[1:]
                continue

            # 如果所有检查都通过，说明我们找到了一个有效的完整数据帧，进行解析
            # 获取用户设置的解析数据组数
            max_groups_to_parse = getattr(self, 'max_groups_to_parse', None)
            self.parse_frame(full_frame_data, max_groups_to_parse)
            processed_frames += 1

            # 从缓冲区中移除已处理的帧
            self.receive_buffer = self.receive_buffer[expected_frame_len:]
        
        # 如果缓冲区中还有数据，安排下一次处理
        if len(self.receive_buffer) > MAIN_HEADER_TOTAL_LEN and self.is_running:
            QTimer.singleShot(0, self.process_buffer)


    def parse_frame(self, frame_data, max_groups_to_parse=None):
        """
        解析一个完整的下位机数据帧。
        Args:
            frame_data (bytes): 包含一个完整数据帧的字节串。
            max_groups_to_parse (int): 最大解析的数据组数，None表示解析全部
        """
        try:
            parsed_data = {}
            offset = 0

            # 包头 (已在process_buffer中检查，此处仅为记录)
            parsed_data['包头'] = hex(frame_data[offset])
            offset += 1

            # 帧ID (uint32_t, 小端序)
            if offset + FRAME_ID_LEN > len(frame_data):
                raise ValueError(f"帧ID数据不足: 需要{FRAME_ID_LEN}字节，剩余{len(frame_data)-offset}字节")
            parsed_data['帧ID'] = struct.unpack('<I', frame_data[offset:offset+FRAME_ID_LEN])[0]
            offset += FRAME_ID_LEN

            # ARM ID (3 x uint32_t, 小端序)
            if offset + ARM_ID_LEN > len(frame_data):
                raise ValueError(f"ARM ID数据不足: 需要{ARM_ID_LEN}字节，剩余{len(frame_data)-offset}字节")
            arm_ids_tuple = struct.unpack('<III', frame_data[offset:offset+ARM_ID_LEN])
            parsed_data['ARM ID'] = [f"{uid:08X}" for uid in arm_ids_tuple] # 格式化为8位十六进制
            offset += ARM_ID_LEN

            # 悬停结果 (uint32_t, 小端序)
            if offset + HOVER_RESULT_LEN > len(frame_data):
                raise ValueError(f"悬停结果数据不足: 需要{HOVER_RESULT_LEN}字节，剩余{len(frame_data)-offset}字节")
            parsed_data['悬停结果'] = struct.unpack('<I', frame_data[offset:offset+HOVER_RESULT_LEN])[0]
            offset += HOVER_RESULT_LEN

            # 悬停计数值 (uint32_t, 小端序)
            if offset + HOVER_COUNT_LEN > len(frame_data):
                raise ValueError(f"悬停计数值数据不足: 需要{HOVER_COUNT_LEN}字节，剩余{len(frame_data)-offset}字节")
            parsed_data['悬停计数值'] = struct.unpack('<I', frame_data[offset:offset+HOVER_COUNT_LEN])[0]
            offset += HOVER_COUNT_LEN

            # 数据组数n (uint32_t, 小端序)
            if offset + NUM_GROUPS_LEN > len(frame_data):
                raise ValueError(f"数据组数n数据不足: 需要{NUM_GROUPS_LEN}字节，剩余{len(frame_data)-offset}字节")
            num_groups = struct.unpack('<I', frame_data[offset:offset+NUM_GROUPS_LEN])[0]
            parsed_data['数据组数n'] = num_groups
            offset += NUM_GROUPS_LEN

            # 验证数据组数的合理性
            if num_groups > 1000:  # 设置一个合理的上限
                raise ValueError(f"数据组数异常: {num_groups}，可能数据损坏")

            # 确定实际要解析的数据组数
            if max_groups_to_parse is None or max_groups_to_parse == 0:
                groups_to_parse = num_groups  # 解析全部
            else:
                groups_to_parse = min(max_groups_to_parse, num_groups)

            # 数据组解析 - 只解析指定数量的数据组
            parsed_data['数据组'] = []
            for i in range(groups_to_parse):
                if offset + DATA_GROUP_LEN > len(frame_data):
                    raise ValueError(f"数据组{i+1}数据不足: 需要{DATA_GROUP_LEN}字节，剩余{len(frame_data)-offset}字节")

                group_data = {}

                # PWM计数值 (int32_t, 小端序)
                group_data['PWM计数值'] = struct.unpack('<i', frame_data[offset:offset+4])[0]
                offset += 4

                # 模拟输出1-4 (4 x int32_t, 小端序)
                group_data['模拟输出1_uV'] = struct.unpack('<i', frame_data[offset:offset+4])[0]
                offset += 4
                group_data['模拟输出2_uV'] = struct.unpack('<i', frame_data[offset:offset+4])[0]
                offset += 4
                group_data['模拟输出3_uV'] = struct.unpack('<i', frame_data[offset:offset+4])[0]
                offset += 4
                group_data['模拟输出4_uV'] = struct.unpack('<i', frame_data[offset:offset+4])[0]
                offset += 4

                # 采样间隔 (uint32_t, 小端序)
                group_data['采样间隔_us'] = struct.unpack('<I', frame_data[offset:offset+4])[0]
                offset += 4

                # 模拟输入1-4 (4 x int16_t, 小端序)
                # 原始采样值 换算公式： 电压值(V) = 原始采样值 * 10 / 32768
                for j in range(1, 5):
                    raw_ai = struct.unpack('<h', frame_data[offset:offset+2])[0]
                    group_data[f'模拟输入{j}_原始值'] = raw_ai
                    group_data[f'模拟输入{j}_V'] = raw_ai * 10.0 / 32768.0
                    offset += 2

                parsed_data['数据组'].append(group_data)

            # 记录实际解析的数据组数和跳过的数据组数
            parsed_data['已解析数据组数'] = groups_to_parse
            parsed_data['跳过数据组数'] = num_groups - groups_to_parse

            # 包尾 (已在process_buffer中检查，此处仅为记录)
            # 读取两个字节，用大端序格式化为单个十六进制数方便显示 0x0D0A
            if len(frame_data) >= FOOTER_LEN:
                parsed_data['包尾'] = hex(struct.unpack('>H', frame_data[-FOOTER_LEN:])[0])
            else:
                parsed_data['包尾'] = "数据不足"

            # 更新统计信息
            self.frame_count += 1
            self.data_group_count += num_groups
            self.latest_frame_data = parsed_data

            # 数据记录功能
            if self.data_logging_enabled and self.log_file:
                self.log_frame_data(parsed_data)

            # 更新波形数据
            self.update_waveform_data(parsed_data)

            # 控制解析数据的GUI更新频率
            current_time = time.time() * 1000
            if current_time - self.last_parsed_update >= PARSED_DATA_UPDATE_INTERVAL_MS:
                self.signals.parsed_data.emit(parsed_data)
                self.last_parsed_update = current_time

            # 仅对统计信息应用更新频率控制
            if current_time - self.last_gui_update >= GUI_UPDATE_INTERVAL_MS:
                # 发送统计信息
                if self.start_time:
                    elapsed_time = time.time() - self.start_time
                    fps = self.frame_count / elapsed_time if elapsed_time > 0 else 0
                    data_rate = self.bytes_received / elapsed_time / 1024 if elapsed_time > 0 else 0  # KB/s

                    stats = {
                        '帧数': self.frame_count,
                        '数据组总数': self.data_group_count,
                        '接收字节数': self.bytes_received,
                        '运行时间': f"{elapsed_time:.1f}s",
                        '帧率': f"{fps:.1f} fps",
                        '数据速率': f"{data_rate:.1f} KB/s"
                    }
                    self.signals.statistics_updated.emit(stats)
                self.last_gui_update = current_time
            elif not hasattr(self, '_stats_sent') or not self._stats_sent:
                # 确保至少发送一次解析数据，即使不在更新间隔内
                self.signals.parsed_data.emit(parsed_data)
                self._stats_sent = True


        except Exception as e:
            # 捕获解析异常，避免整个解析线程崩溃
            error_msg = f"解析帧数据时发生错误: {e}"
            print(f"ERROR: {error_msg}")

            # 打印更详细的调试信息
            if len(frame_data) > 0:
                debug_hex = ' '.join(f'{byte:02X}' for byte in frame_data[:min(64, len(frame_data))])
                print(f"ERROR: 问题帧数据 (前64字节): {debug_hex}")
                print(f"ERROR: 帧总长度: {len(frame_data)}")

            # 发送错误信号到GUI，但不中断整个接收过程
            self.signals.error.emit(f"数据解析错误: {e}")
            return  # 跳过这一帧，继续处理下一帧

    def log_frame_data(self, parsed_data):
        """优化数据记录，按帧分组显示，去掉时间戳"""
        try:
            if not self.log_file:
                return

            # 获取选中的通道
            selected_channels = []
            if hasattr(self, 'main_window_ref') and self.main_window_ref and self.main_window_ref():
                selected_channels = self.main_window_ref().get_selected_channels()

            # 如果没有选中任何通道，跳过记录
            if not selected_channels:
                return

            # 通道映射字典
            channel_mapping = {
                'frame_id': '帧ID',
                'pwm_count': 'PWM计数值',
                'analog_out_1': '模拟输出1_uV',
                'analog_out_2': '模拟输出2_uV',
                'analog_out_3': '模拟输出3_uV',
                'analog_out_4': '模拟输出4_uV',
                'sample_interval': '采样间隔_us',
                'analog_in_1_raw': '模拟输入1_原始值',
                'analog_in_1_v': '模拟输入1_V',
                'analog_in_2_raw': '模拟输入2_原始值',
                'analog_in_2_v': '模拟输入2_V',
                'analog_in_3_raw': '模拟输入3_原始值',
                'analog_in_3_v': '模拟输入3_V',
                'analog_in_4_raw': '模拟输入4_原始值',
                'analog_in_4_v': '模拟输入4_V'
            }

            # 写入帧头信息
            frame_id = parsed_data['帧ID']
            data_group_count = len(parsed_data['数据组'])

            # 构建通道名称列表（用于显示）
            channel_names = []
            for channel in selected_channels:
                if channel in channel_mapping:
                    channel_names.append(channel_mapping[channel])

            # 写入帧分隔符和基本信息
            self.log_file.write(f"----- 帧ID: {frame_id} -----\n")
            self.log_file.write(f"数据组数: {data_group_count}\n")
            self.log_file.write(f"选中通道: {', '.join(channel_names)}\n")
            self.log_file.write("数据:\n")

            # 写入每组数据
            for i, group in enumerate(parsed_data['数据组']):
                data_values = []

                # 提取选中通道的数据
                for channel in selected_channels:
                    if channel == 'frame_id':
                        data_values.append(str(parsed_data['帧ID']))
                    elif channel == 'pwm_count':
                        data_values.append(str(group['PWM计数值']))
                    elif channel == 'sample_interval':
                        data_values.append(str(group['采样间隔_us']))
                    elif channel == 'analog_out_1':
                        data_values.append(str(group['模拟输出1_uV']))
                    elif channel == 'analog_out_2':
                        data_values.append(str(group['模拟输出2_uV']))
                    elif channel == 'analog_out_3':
                        data_values.append(str(group['模拟输出3_uV']))
                    elif channel == 'analog_out_4':
                        data_values.append(str(group['模拟输出4_uV']))
                    elif channel == 'analog_in_1_raw':
                        data_values.append(str(group['模拟输入1_原始值']))
                    elif channel == 'analog_in_1_v':
                        data_values.append(str(group['模拟输入1_V']))
                    elif channel == 'analog_in_2_raw':
                        data_values.append(str(group['模拟输入2_原始值']))
                    elif channel == 'analog_in_2_v':
                        data_values.append(str(group['模拟输入2_V']))
                    elif channel == 'analog_in_3_raw':
                        data_values.append(str(group['模拟输入3_原始值']))
                    elif channel == 'analog_in_3_v':
                        data_values.append(str(group['模拟输入3_V']))
                    elif channel == 'analog_in_4_raw':
                        data_values.append(str(group['模拟输入4_原始值']))
                    elif channel == 'analog_in_4_v':
                        data_values.append(str(group['模拟输入4_V']))

                # 写入数据行
                if data_values:
                    if len(selected_channels) == 1:
                        # 如果只选择了一个通道，直接显示值
                        self.log_file.write(f"{data_values[0]}\n")
                    else:
                        # 如果选择了多个通道，用逗号分隔
                        self.log_file.write(f"[{','.join(data_values)}]\n")

            # 写入帧结束标记
            self.log_file.write("\n")
            self.log_file.flush()

        except Exception as e:
            print(f"记录数据时出错: {e}")

    def enable_data_logging(self, file_path, main_window_ref=None):
        """
        启用数据记录功能
        Args:
            file_path (str): 日志文件路径
            main_window_ref: 主窗口的弱引用，用于获取通道选择
        """
        try:
            self.log_file_path = file_path
            self.log_file = open(file_path, 'w', encoding='utf-8')
            self.data_logging_enabled = True
            self.main_window_ref = main_window_ref

            # 获取选中的通道信息
            if main_window_ref and main_window_ref():
                selected_channels = main_window_ref().get_selected_channels()
                channel_names = []

                # 构建通道名称列表
                channel_mapping = {
                    'frame_id': '帧ID',
                    'pwm_count': 'PWM计数值',
                    'analog_out_1': '模拟输出1_uV',
                    'analog_out_2': '模拟输出2_uV',
                    'analog_out_3': '模拟输出3_uV',
                    'analog_out_4': '模拟输出4_uV',
                    'sample_interval': '采样间隔_us',
                    'analog_in_1_raw': '模拟输入1_原始值',
                    'analog_in_1_v': '模拟输入1_V',
                    'analog_in_2_raw': '模拟输入2_原始值',
                    'analog_in_2_v': '模拟输入2_V',
                    'analog_in_3_raw': '模拟输入3_原始值',
                    'analog_in_3_v': '模拟输入3_V',
                    'analog_in_4_raw': '模拟输入4_原始值',
                    'analog_in_4_v': '模拟输入4_V'
                }

                for channel in selected_channels:
                    if channel in channel_mapping:
                        channel_names.append(channel_mapping[channel])

                data_format = f"[{','.join(channel_names)}]"
            else:
                data_format = "[所有通道数据]"

            # 写入文件头部信息
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.log_file.write(f"# 网口通信数据记录\n")
            self.log_file.write(f"# 开始时间: {timestamp}\n")
            self.log_file.write(f"# 选中通道数: {len(selected_channels) if main_window_ref and main_window_ref() else '全部'}\n")
            self.log_file.write(f"# 数据格式: 按帧分组显示，每帧包含所有数据组\n")
            self.log_file.write(f"# 选中通道: {data_format}\n")
            self.log_file.write(f"# ==========================================\n\n")
            self.log_file.flush()

            return True
        except Exception as e:
            print(f"启用数据记录失败: {e}")
            return False

    def disable_data_logging(self):
        """
        禁用数据记录功能
        """
        self.data_logging_enabled = False
        if self.log_file:
            try:
                # 写入结束信息
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.log_file.write(f"\n# 记录结束时间: {timestamp}\n")
                self.log_file.write(f"# 总帧数: {self.frame_count}\n")
                self.log_file.close()
            except Exception as e:
                print(f"关闭日志文件时出错: {e}")
            finally:
                self.log_file = None

    def stop(self):
        """停止线程并关闭套接字"""
        self.is_running = False

        # 关闭数据记录
        self.disable_data_logging()

        if self.socket:
            try:
                # 尝试优雅地关闭套接字，禁用读写
                self.socket.shutdown(socket.SHUT_RDWR)
                self.socket.close()
            except OSError as e:
                print(f"关闭套接字时出错: {e}")
            except Exception as e:
                print(f"关闭套接字时发生未知错误: {e}")
        self.wait() # 等待线程完全结束
        self.signals.disconnected.emit() # 发送断开连接信号

    def stop_client(self):
        """强制停止客户端连接"""
        self.is_running = False

        # 关闭数据记录
        self.disable_data_logging()

        if self.socket:
            try:
                self.socket.close()
            except Exception as e:
                print(f"关闭套接字时出错: {e}")
        self.signals.disconnected.emit() # 发送断开连接信号

    def set_parse_groups(self, max_groups):
        """设置最大解析数据组数"""
        self.max_groups_to_parse = max_groups if max_groups > 0 else None

    def init_waveform_buffers(self):
        """初始化波形数据缓冲区"""
        channel_keys = [
            'frame_id', 'pwm_count', 'analog_out_1', 'analog_out_2', 'analog_out_3', 'analog_out_4',
            'sample_interval', 'analog_in_1_raw', 'analog_in_1_v', 'analog_in_2_raw', 'analog_in_2_v',
            'analog_in_3_raw', 'analog_in_3_v', 'analog_in_4_raw', 'analog_in_4_v'
        ]

        for channel_key in channel_keys:
            # 初始化为NaN值的固定大小数组，表示没有数据
            self.waveform_data[channel_key] = [np.nan] * self.waveform_buffer_size

    def update_waveform_data(self, parsed_data):
        """示波器式更新波形数据缓存 - 清理旧数据"""
        try:
            data_groups = parsed_data['数据组']
            num_groups = len(data_groups)

            if num_groups == 0:
                return

            # 为每个数据组更新波形数据
            for group in data_groups:
                # 检查是否需要循环回到开头
                if self.waveform_x_index >= self.waveform_buffer_size:
                    self.waveform_x_index = 0
                    self.waveform_has_wrapped = True  # 标记已经循环过一次

                # 在写入新数据之前，先清理前面一段区域的旧数据
                # 这样可以避免新旧数据混合显示
                if self.waveform_has_wrapped:
                    # 清理当前位置前面的一小段数据，创建"空白区域"
                    clear_range = min(50, self.waveform_buffer_size // 20)  # 清理50个点或缓冲区的5%
                    for i in range(clear_range):
                        clear_index = (self.waveform_x_index + i + 1) % self.waveform_buffer_size
                        for channel_key in self.waveform_data:
                            self.waveform_data[channel_key][clear_index] = np.nan

                # 更新各通道数据
                self.waveform_data['frame_id'][self.waveform_x_index] = parsed_data['帧ID']
                self.waveform_data['pwm_count'][self.waveform_x_index] = group['PWM计数值']
                self.waveform_data['sample_interval'][self.waveform_x_index] = group['采样间隔_us']
                self.waveform_data['analog_out_1'][self.waveform_x_index] = group['模拟输出1_uV']
                self.waveform_data['analog_out_2'][self.waveform_x_index] = group['模拟输出2_uV']
                self.waveform_data['analog_out_3'][self.waveform_x_index] = group['模拟输出3_uV']
                self.waveform_data['analog_out_4'][self.waveform_x_index] = group['模拟输出4_uV']
                self.waveform_data['analog_in_1_raw'][self.waveform_x_index] = group['模拟输入1_原始值']
                self.waveform_data['analog_in_1_v'][self.waveform_x_index] = group['模拟输入1_V']
                self.waveform_data['analog_in_2_raw'][self.waveform_x_index] = group['模拟输入2_原始值']
                self.waveform_data['analog_in_2_v'][self.waveform_x_index] = group['模拟输入2_V']
                self.waveform_data['analog_in_3_raw'][self.waveform_x_index] = group['模拟输入3_原始值']
                self.waveform_data['analog_in_3_v'][self.waveform_x_index] = group['模拟输入3_V']
                self.waveform_data['analog_in_4_raw'][self.waveform_x_index] = group['模拟输入4_原始值']
                self.waveform_data['analog_in_4_v'][self.waveform_x_index] = group['模拟输入4_V']

                # 移动到下一个位置
                self.waveform_x_index += 1

                # 更新有效数据点数量
                if not self.waveform_has_wrapped:
                    self.waveform_valid_data_count = self.waveform_x_index
                else:
                    self.waveform_valid_data_count = self.waveform_buffer_size

        except Exception as e:
            print(f"更新波形数据时出错: {e}")

    def get_waveform_data(self, channel_key):
        """获取指定通道的波形数据 - 示波器模式，波形从左到右前进"""
        if channel_key not in self.waveform_data:
            return [], []

        if not self.waveform_has_wrapped:
            # 还没有循环过，按原始顺序显示数据
            if self.waveform_x_index > 0:
                x_data = list(range(self.waveform_x_index))
                y_data = self.waveform_data[channel_key][:self.waveform_x_index]
                return x_data, y_data
        else:
            # 已经循环过，按照示波器模式重新排列数据
            # 目标：让波形看起来是从左到右连续前进的

            # 方案：保持原始的索引位置，但让pyqtgraph只显示有效数据
            # 这样可以实现真正的示波器效果：新数据覆盖旧数据位置
            x_data = list(range(self.waveform_buffer_size))
            y_data = self.waveform_data[channel_key].copy()

            return x_data, y_data

        return [], []


# --- 鲁棒发送线程 ---
# 负责可靠的指令发送，每发送一个指令等待回显确认后再发送下一个
class RobustSender(QThread):
    def __init__(self, socket_ref, parent=None):
        super().__init__(parent)
        self.socket_ref = socket_ref  # 套接字引用（来自TcpClientWorker）
        self.signals = SenderSignals()
        self.command_queue = []  # 待发送的指令队列 [(command_num, command_value), ...]
        self.is_running = False
        self.current_command = None  # 当前正在等待确认的指令
        self.confirmation_received = False  # 确认接收标志
        self.send_timeout = 2.0  # 发送超时时间（秒）
        self.max_retries = 3  # 最大重试次数

    def add_command(self, command_num, command_value):
        """添加指令到发送队列"""
        self.command_queue.append((command_num, command_value))

    def add_commands(self, commands):
        """批量添加指令到发送队列
        Args:
            commands: [(command_num, command_value), ...] 指令列表
        """
        self.command_queue.extend(commands)

    def clear_queue(self):
        """清空发送队列"""
        self.command_queue.clear()

    def on_command_confirmed(self, command_num, command_value):
        """接收到指令确认时的回调"""
        if self.current_command and self.current_command == (command_num, command_value):
            self.confirmation_received = True
            print(f"🔄 发送线程收到确认 - 序号:{command_num}, 值:{command_value}")

    def run(self):
        """发送线程主循环"""
        self.is_running = True
        total_commands = len(self.command_queue)
        sent_count = 0

        print(f"🚀 开始鲁棒发送，共{total_commands}个指令")

        while self.is_running and self.command_queue:
            command_num, command_value = self.command_queue.pop(0)
            self.current_command = (command_num, command_value)

            # 尝试发送指令（带重试机制）
            success = False
            for retry in range(self.max_retries):
                if not self.is_running:
                    break

                try:
                    # 构建并发送指令帧
                    frame = self.build_command_frame(command_num, command_value)
                    if self.socket_ref() and self.socket_ref().socket:
                        self.socket_ref().socket.send(frame)
                        hex_string = ' '.join(f'{byte:02X}' for byte in frame)
                        print(f"📤 发送指令 (尝试{retry+1}/{self.max_retries}) - 序号:{command_num}, 值:{command_value}")
                        print(f"📤 指令帧: {hex_string}")

                        # 发送指令发送信号
                        self.signals.command_sent.emit(command_num, command_value)

                        # 等待确认
                        self.confirmation_received = False
                        if self.wait_for_confirmation():
                            success = True
                            sent_count += 1
                            self.signals.command_confirmed.emit(command_num, command_value)
                            self.signals.send_progress.emit(sent_count, total_commands)
                            print(f"✅ 指令发送成功 - 序号:{command_num}, 值:{command_value} ({sent_count}/{total_commands})")
                            break
                        else:
                            print(f"⏰ 指令确认超时 - 序号:{command_num}, 值:{command_value} (尝试{retry+1}/{self.max_retries})")
                            if retry < self.max_retries - 1:
                                self.msleep(100)  # 重试前短暂等待
                    else:
                        self.signals.send_error.emit("套接字未连接")
                        return

                except Exception as e:
                    print(f"❌ 发送指令失败 - 序号:{command_num}, 值:{command_value}, 错误:{e}")
                    if retry < self.max_retries - 1:
                        self.msleep(100)  # 重试前短暂等待

            if not success:
                # 所有重试都失败了
                self.signals.command_timeout.emit(command_num, command_value)
                print(f"💥 指令发送最终失败 - 序号:{command_num}, 值:{command_value}")

            self.current_command = None

            # 指令间延时
            if self.command_queue:  # 如果还有更多指令
                self.msleep(50)  # 50ms延时

        print(f"🏁 鲁棒发送完成，成功发送{sent_count}/{total_commands}个指令")
        self.signals.send_completed.emit()
        self.is_running = False

    def build_command_frame(self, command_num, command_value):
        """构建指令帧"""
        frame = bytearray()
        frame.append(0xBB)  # 包头
        frame.append(command_num)  # 指令序号 (u8)
        frame.extend(struct.pack('<I', command_value))  # 指令值 (小端序u32)
        frame.extend(b'\x0D\x0A')  # 包尾
        return bytes(frame)

    def wait_for_confirmation(self):
        """等待指令确认，带超时"""
        timeout_ms = int(self.send_timeout * 1000)
        elapsed_ms = 0
        check_interval_ms = 10

        while elapsed_ms < timeout_ms and self.is_running:
            if self.confirmation_received:
                return True
            self.msleep(check_interval_ms)
            elapsed_ms += check_interval_ms

        return False

    def stop(self):
        """停止发送线程"""
        self.is_running = False
        self.wait()  # 等待线程结束


# --- GUI主窗口 ---
class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("下位机TCP数据接收器 📡")
        self.setGeometry(100, 100, 1000, 700) # 设置窗口初始位置和大小
        self.init_ui() # 初始化用户界面

        self.client_thread = None # TCP客户端线程实例
        self.sender_thread = None # 鲁棒发送线程实例

    def init_ui(self):
        """初始化窗口的布局和控件"""
        main_layout = QVBoxLayout()

        # 连接设置组框
        conn_group = QGroupBox("连接设置 ⚙️")
        conn_layout = QHBoxLayout()

        conn_layout.addWidget(QLabel("IP地址:"))
        self.ip_input = QLineEdit(DEFAULT_TCP_IP) # 默认IP
        conn_layout.addWidget(self.ip_input)

        conn_layout.addWidget(QLabel("端口:"))
        self.port_input = QLineEdit(str(DEFAULT_TCP_PORT)) # 默认端口
        conn_layout.addWidget(self.port_input)

        self.connect_button = QPushButton("连接 ▶️")
        self.connect_button.clicked.connect(self.connect_tcp)
        conn_layout.addWidget(self.connect_button)

        self.disconnect_button = QPushButton("断开 ⏹️")
        self.disconnect_button.clicked.connect(self.disconnect_tcp)
        self.disconnect_button.setEnabled(False) # 初始状态为禁用
        conn_layout.addWidget(self.disconnect_button)

        self.status_label = QLabel("状态: 未连接 🔴") # 连接状态标签
        conn_layout.addWidget(self.status_label)
        conn_group.setLayout(conn_layout)
        main_layout.addWidget(conn_group)

        # 指令发送组框
        send_group = QGroupBox("指令发送 📤")
        send_layout = QHBoxLayout()

        send_layout.addWidget(QLabel("指令序号:"))
        self.command_input = QLineEdit()
        self.command_input.setPlaceholderText("输入指令序号 (u8, 0-255)")
        send_layout.addWidget(self.command_input)

        send_layout.addWidget(QLabel("指令值:"))
        self.value_input = QLineEdit()
        self.value_input.setPlaceholderText("输入指令值 (u32, 0-4294967295)")
        send_layout.addWidget(self.value_input)

        self.send_button = QPushButton("发送指令 📡")
        self.send_button.clicked.connect(self.send_command)
        self.send_button.setEnabled(False)  # 初始状态为禁用，连接后启用
        send_layout.addWidget(self.send_button)

        send_group.setLayout(send_layout)
        main_layout.addWidget(send_group)

        # Piezo控制组框
        piezo_group = QGroupBox("Piezo控制 🔧")
        piezo_layout = QVBoxLayout()

        # Piezo初始化按钮
        piezo_init_layout = QHBoxLayout()
        self.piezo_init_button = QPushButton("Piezo初始化 ⚙️")
        self.piezo_init_button.clicked.connect(self.piezo_initialize)
        self.piezo_init_button.setEnabled(False)  # 初始状态为禁用，连接后启用
        piezo_init_layout.addWidget(self.piezo_init_button)

        self.piezo_status_label = QLabel("状态: 未初始化")
        self.piezo_status_label.setStyleSheet("color: gray;")
        piezo_init_layout.addWidget(self.piezo_status_label)
        piezo_layout.addLayout(piezo_init_layout)

        # Piezo扫描控制
        piezo_scan_layout = QHBoxLayout()
        self.piezo_scan_up_button = QPushButton("Piezo向上扫描 ⬆️")
        self.piezo_scan_up_button.clicked.connect(self.piezo_scan_up)
        self.piezo_scan_up_button.setEnabled(False)  # 初始状态为禁用
        piezo_scan_layout.addWidget(self.piezo_scan_up_button)

        self.piezo_scan_down_button = QPushButton("Piezo向下扫描 ⬇️")
        self.piezo_scan_down_button.clicked.connect(self.piezo_scan_down)
        self.piezo_scan_down_button.setEnabled(False)  # 初始状态为禁用
        piezo_scan_layout.addWidget(self.piezo_scan_down_button)

        self.piezo_stop_button = QPushButton("停止扫描 ⏹️")
        self.piezo_stop_button.clicked.connect(self.piezo_stop_scan)
        self.piezo_stop_button.setEnabled(False)  # 初始状态为禁用
        piezo_scan_layout.addWidget(self.piezo_stop_button)
        piezo_layout.addLayout(piezo_scan_layout)

        piezo_group.setLayout(piezo_layout)
        main_layout.addWidget(piezo_group)

        # 数据记录组框
        logging_group = QGroupBox("数据记录 📝")
        logging_main_layout = QVBoxLayout()

        # 第一行：基本控制
        logging_control_layout = QHBoxLayout()
        self.logging_checkbox = QCheckBox("启用数据记录")
        self.logging_checkbox.stateChanged.connect(self.toggle_data_logging)
        logging_control_layout.addWidget(self.logging_checkbox)

        self.select_file_button = QPushButton("选择记录文件 📁")
        self.select_file_button.clicked.connect(self.select_log_file)
        logging_control_layout.addWidget(self.select_file_button)

        self.log_file_label = QLabel("未选择文件")
        self.log_file_label.setStyleSheet("color: gray; font-style: italic;")
        logging_control_layout.addWidget(self.log_file_label)

        logging_main_layout.addLayout(logging_control_layout)

        # 第二行：通道选择
        channel_selection_group = QGroupBox("通道选择 🎛️")
        channel_layout = QGridLayout()

        # 初始化通道选择字典
        self.channel_selections = {}

        # 基础数据通道
        basic_channels = [
            ('frame_id', '帧ID'),
            ('pwm_count', 'PWM计数值'),
            ('sample_interval', '采样间隔(us)')
        ]

        # 模拟输出通道
        analog_output_channels = [
            ('analog_out_1', '模拟输出1(uV)'),
            ('analog_out_2', '模拟输出2(uV)'),
            ('analog_out_3', '模拟输出3(uV)'),
            ('analog_out_4', '模拟输出4(uV)')
        ]

        # 模拟输入通道
        analog_input_channels = [
            ('analog_in_1_raw', '模拟输入1_原始值'),
            ('analog_in_1_v', '模拟输入1_电压(V)'),
            ('analog_in_2_raw', '模拟输入2_原始值'),
            ('analog_in_2_v', '模拟输入2_电压(V)'),
            ('analog_in_3_raw', '模拟输入3_原始值'),
            ('analog_in_3_v', '模拟输入3_电压(V)'),
            ('analog_in_4_raw', '模拟输入4_原始值'),
            ('analog_in_4_v', '模拟输入4_电压(V)')
        ]

        # 添加全选/全不选按钮
        select_all_button = QPushButton("全选")
        select_all_button.clicked.connect(self.select_all_channels)
        channel_layout.addWidget(select_all_button, 0, 0)

        select_none_button = QPushButton("全不选")
        select_none_button.clicked.connect(self.select_no_channels)
        channel_layout.addWidget(select_none_button, 0, 1)

        # 添加预设按钮
        preset_basic_button = QPushButton("基础数据")
        preset_basic_button.clicked.connect(self.select_basic_channels)
        channel_layout.addWidget(preset_basic_button, 0, 2)

        preset_analog_button = QPushButton("模拟通道")
        preset_analog_button.clicked.connect(self.select_analog_channels)
        channel_layout.addWidget(preset_analog_button, 0, 3)

        # 添加通道复选框
        row = 1
        col = 0
        max_cols = 4

        all_channels = basic_channels + analog_output_channels + analog_input_channels

        for channel_key, channel_name in all_channels:
            checkbox = QCheckBox(channel_name)
            checkbox.setChecked(True)  # 默认全选
            self.channel_selections[channel_key] = checkbox
            channel_layout.addWidget(checkbox, row, col)

            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        channel_selection_group.setLayout(channel_layout)
        logging_main_layout.addWidget(channel_selection_group)

        logging_group.setLayout(logging_main_layout)
        main_layout.addWidget(logging_group)

        # 波形显示组框
        waveform_group = QGroupBox("波形显示 📈")
        waveform_layout = QVBoxLayout()

        # 波形控制区域
        waveform_control_layout = QHBoxLayout()

        waveform_control_layout.addWidget(QLabel("显示通道:"))
        self.waveform_channel_combo = QComboBox()
        self.waveform_channel_combo.addItems([
            "模拟输出1_uV", "模拟输出2_uV", "模拟输出3_uV", "模拟输出4_uV",
            "模拟输入1_V", "模拟输入2_V", "模拟输入3_V", "模拟输入4_V",
            "模拟输入1_原始值", "模拟输入2_原始值", "模拟输入3_原始值", "模拟输入4_原始值",
            "PWM计数值", "采样间隔_us", "帧ID"
        ])
        self.waveform_channel_combo.setCurrentText("模拟输出2_uV")  # 默认选择
        waveform_control_layout.addWidget(self.waveform_channel_combo)

        waveform_control_layout.addWidget(QLabel("X轴范围:"))
        self.waveform_range_label = QLabel("0 - 100000 (示波器模式)")
        self.waveform_range_label.setStyleSheet("color: blue; font-weight: bold;")
        waveform_control_layout.addWidget(self.waveform_range_label)

        self.waveform_enable_checkbox = QCheckBox("启用波形显示")
        self.waveform_enable_checkbox.setChecked(True)
        waveform_control_layout.addWidget(self.waveform_enable_checkbox)

        waveform_control_layout.addStretch()
        waveform_layout.addLayout(waveform_control_layout)

        # 创建pyqtgraph绘图区域
        self.waveform_widget = pg.PlotWidget()
        self.waveform_widget.setBackground('w')  # 白色背景
        self.waveform_widget.setLabel('left', '数值')
        self.waveform_widget.setLabel('bottom', '数据点索引')
        self.waveform_widget.showGrid(x=True, y=True, alpha=0.3)
        self.waveform_widget.setMinimumHeight(300)

        # 设置固定的X轴范围（示波器模式）
        self.waveform_widget.setXRange(0, 100000, padding=0)
        self.waveform_widget.enableAutoRange(axis='x', enable=False)  # 禁用X轴自动缩放

        # 设置绘图样式
        self.waveform_curve = self.waveform_widget.plot(pen=pg.mkPen(color='b', width=2))

        # 添加当前写入位置指示线
        self.current_pos_line = self.waveform_widget.addLine(x=0, pen=pg.mkPen(color='r', width=2, style=pg.QtCore.Qt.PenStyle.DashLine))

        waveform_layout.addWidget(self.waveform_widget)
        waveform_group.setLayout(waveform_layout)
        main_layout.addWidget(waveform_group)

        # 波形更新定时器
        self.waveform_timer = QTimer()
        self.waveform_timer.timeout.connect(self.update_waveform_display)
        self.waveform_timer.setInterval(100)  # 100ms更新一次

        # 数据显示组框 - 只显示解析后的数据
        data_display_group = QGroupBox("数据解析显示 📊")
        data_display_layout = QVBoxLayout()

        # 解析控制区域
        parse_control_layout = QHBoxLayout()
        parse_control_layout.addWidget(QLabel("解析数据组数:"))
        self.parse_groups_input = QLineEdit("10")  # 默认解析前10组
        self.parse_groups_input.setPlaceholderText("输入要解析的数据组数 (0=全部)")
        self.parse_groups_input.setMaximumWidth(150)
        self.parse_groups_input.textChanged.connect(self.on_parse_groups_changed)
        parse_control_layout.addWidget(self.parse_groups_input)

        parse_control_layout.addWidget(QLabel("显示数据组数:"))
        self.display_groups_input = QLineEdit("2")  # 默认显示前2组
        self.display_groups_input.setPlaceholderText("输入要显示的数据组数")
        self.display_groups_input.setMaximumWidth(150)
        parse_control_layout.addWidget(self.display_groups_input)

        parse_control_layout.addStretch()  # 添加弹性空间
        data_display_layout.addLayout(parse_control_layout)

        # 解析后数据显示区域
        data_display_layout.addWidget(QLabel("解析后数据 (最新一帧):"))
        self.parsed_data_browser = QTextBrowser()
        self.parsed_data_browser.setPlaceholderText("等待解析数据...")
        data_display_layout.addWidget(self.parsed_data_browser)

        data_display_group.setLayout(data_display_layout)
        main_layout.addWidget(data_display_group)

        # 统计信息组框
        stats_group = QGroupBox("性能统计 📈")
        stats_layout = QHBoxLayout()

        self.stats_label = QLabel("等待连接...")
        self.stats_label.setStyleSheet("font-family: monospace; font-size: 10pt;")
        stats_layout.addWidget(self.stats_label)

        stats_group.setLayout(stats_layout)
        main_layout.addWidget(stats_group)

        self.setLayout(main_layout)

        # 数据记录相关变量
        self.selected_log_file = None

        # Piezo控制相关变量
        self.piezo_initialized = False

    def on_parse_groups_changed(self):
        """当解析数据组数输入框内容改变时调用"""
        try:
            max_groups = int(self.parse_groups_input.text())
            if self.client_thread and self.client_thread.isRunning():
                self.client_thread.set_parse_groups(max_groups)
                print(f"更新解析数据组数: {max_groups if max_groups > 0 else '全部'}")
        except ValueError:
            pass  # 忽略无效输入

    def select_log_file(self):
        """选择数据记录文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"网口数据记录_{timestamp}.txt"

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "选择数据记录文件",
            default_filename,
            "文本文件 (*.txt);;所有文件 (*)"
        )

        if file_path:
            self.selected_log_file = file_path
            # 显示文件名（不显示完整路径）
            filename = os.path.basename(file_path)
            self.log_file_label.setText(f"已选择: {filename}")
            self.log_file_label.setStyleSheet("color: green;")
        else:
            self.selected_log_file = None
            self.log_file_label.setText("未选择文件")
            self.log_file_label.setStyleSheet("color: gray; font-style: italic;")

    def toggle_data_logging(self, state):
        """切换数据记录状态"""
        if state == 2:  # 选中状态
            if not self.selected_log_file:
                # 如果没有选择文件，自动弹出文件选择对话框
                self.select_log_file()
                if not self.selected_log_file:
                    # 如果用户取消了文件选择，取消勾选
                    self.logging_checkbox.setChecked(False)
                    return

            # 启用数据记录
            if self.client_thread and self.client_thread.isRunning():
                import weakref
                success = self.client_thread.enable_data_logging(self.selected_log_file, weakref.ref(self))
                if success:
                    selected_count = len(self.get_selected_channels())
                    self.log_file_label.setText(f"正在记录: {os.path.basename(self.selected_log_file)} ({selected_count}通道)")
                    self.log_file_label.setStyleSheet("color: blue; font-weight: bold;")
                else:
                    self.logging_checkbox.setChecked(False)
                    self.log_file_label.setText("记录启动失败")
                    self.log_file_label.setStyleSheet("color: red;")
            else:
                # 如果没有连接，只是标记为准备记录
                self.log_file_label.setText(f"准备记录: {os.path.basename(self.selected_log_file)}")
                self.log_file_label.setStyleSheet("color: orange;")
        else:  # 未选中状态
            # 禁用数据记录
            if self.client_thread and self.client_thread.isRunning():
                self.client_thread.disable_data_logging()

            if self.selected_log_file:
                self.log_file_label.setText(f"已选择: {os.path.basename(self.selected_log_file)}")
                self.log_file_label.setStyleSheet("color: green;")
            else:
                self.log_file_label.setText("未选择文件")
                self.log_file_label.setStyleSheet("color: gray; font-style: italic;")

    def piezo_initialize(self):
        """Piezo初始化 - 发送所有Piezo相关的配置参数"""
        if not self.client_thread or not self.client_thread.isRunning():
            self.piezo_status_label.setText("状态: 未连接，无法初始化")
            self.piezo_status_label.setStyleSheet("color: red;")
            return

        try:
            # Piezo配置参数 (基于settings.json中的Piezo配置)
            # 这些参数对应settings.json中Controller.Piezo的各项配置
            piezo_commands = [
                # ADC 采样间隔 (item: 5, value: 50)
                (5, 50),
                # Piezo State (item: 33, value: 0)
                (33, 0),
                # Piezo Voltage (item: 34, value: 1000000) - 1V in uV
                (34, 1000000),
                # Piezo dT (item: 35, value: 100) - 扫描时间间隔
                (35, 1000000),
                # Piezo +dV (item: 36, value: 400) - 正向扫描步长
                (36, 1),
                # Piezo -dV (item: 37, value: 150) - 负向扫描步长
                (37, 1),
                # Piezo ScanUpper (item: 38, value: 10000000) - 扫描上限 10V in uV
                (38, 10000000),
                # Piezo ScanLower (item: 39, value: 0) - 扫描下限
                (39, 0),
                # Piezo Delay+Enable (item: 40, value: 0) - 正向延时使能
                (40, 0),
                # Piezo Delay-Enable (item: 41, value: 0) - 负向延时使能
                (41, 0),
                # Piezo Delay+dT (item: 42, value: 1000) - 正向延时时间
                (42, 1000),
                # Piezo Delay-dT (item: 43, value: 500) - 负向延时时间
                (43, 500),
                # Piezo SingleScanDir (item: 44, value: 0) - 单次扫描方向
                (44, 0),
                # Piezo SingleScanUpper (item: 45, value: 10000000) - 单次扫描上限
                (45, 10000000),
                # Piezo SingleScanLower (item: 46, value: 0) - 单次扫描下限
                (46, 0),
                # Piezo WaveType (item: 47, value: 0) - 波形类型
                (47, 0),
            ]

            # 使用鲁棒发送来发送所有Piezo配置指令
            if self.sender_thread:
                # 清空之前的队列，添加所有Piezo配置指令
                self.sender_thread.clear_queue()
                self.sender_thread.add_commands(piezo_commands)

                # 连接发送完成信号来处理初始化完成
                self.sender_thread.signals.send_completed.connect(self.on_piezo_init_completed)

                # 如果发送线程没有运行，启动它
                if not self.sender_thread.isRunning():
                    self.sender_thread.start()

                self.piezo_status_label.setText(f"状态: 正在初始化... (0/{len(piezo_commands)})")
                self.piezo_status_label.setStyleSheet("color: blue;")
                print(f"开始Piezo初始化，准备发送{len(piezo_commands)}个配置参数")
            else:
                self.piezo_status_label.setText("状态: 发送线程未初始化")
                self.piezo_status_label.setStyleSheet("color: red;")

        except Exception as e:
            self.piezo_status_label.setText(f"状态: 初始化失败 - {e}")
            self.piezo_status_label.setStyleSheet("color: red;")
            print(f"Piezo初始化失败: {e}")

    def piezo_scan_up(self):
        """Piezo向上扫描"""
        if not self.client_thread or not self.client_thread.isRunning():
            self.status_label.setText("状态: 未连接，无法执行扫描 ❌")
            return

        if not self.piezo_initialized:
            self.status_label.setText("状态: Piezo未初始化，请先初始化 ❌")
            return

        try:
            # 1. 设置Piezo模式为SingleScan (指令序号: 17+16=33, 值: 4)
            self.send_piezo_command(33, 4, "设置SingleScan模式")
            time.sleep(0.1)

            # 2. 执行单次向上扫描 (指令序号: 28+16=44, 值: 0)
            self.send_piezo_command(44, 0, "执行向上扫描")

            self.status_label.setText("状态: Piezo向上扫描已启动 ⬆️")
            print("Piezo向上扫描指令已发送")

        except Exception as e:
            self.status_label.setText(f"状态: Piezo向上扫描失败 - {e} ❌")
            print(f"Piezo向上扫描失败: {e}")

    def piezo_scan_down(self):
        """Piezo向下扫描"""
        if not self.client_thread or not self.client_thread.isRunning():
            self.status_label.setText("状态: 未连接，无法执行扫描 ❌")
            return

        if not self.piezo_initialized:
            self.status_label.setText("状态: Piezo未初始化，请先初始化 ❌")
            return

        try:
            # 1. 设置Piezo模式为SingleScan (指令序号: 17+16=33, 值: 4)
            self.send_piezo_command(33, 4, "设置SingleScan模式")
            time.sleep(0.3)

            # 2. 执行单次向下扫描 (指令序号: 28+16=44, 值: 1)
            self.send_piezo_command(44, 1, "执行向下扫描")

            self.status_label.setText("状态: Piezo向下扫描已启动 ⬇️")
            print("Piezo向下扫描指令已发送")

        except Exception as e:
            self.status_label.setText(f"状态: Piezo向下扫描失败 - {e} ❌")
            print(f"Piezo向下扫描失败: {e}")

    def piezo_stop_scan(self):
        """停止Piezo扫描"""
        if not self.client_thread or not self.client_thread.isRunning():
            self.status_label.setText("状态: 未连接，无法停止扫描 ❌")
            return

        try:
            # 设置Piezo模式为Hold (指令序号: 17+16=33, 值: 1)
            self.send_piezo_command(33, 1, "设置Hold模式停止扫描")

            self.status_label.setText("状态: Piezo扫描已停止 ⏹️")
            print("Piezo扫描停止指令已发送")

        except Exception as e:
            self.status_label.setText(f"状态: 停止Piezo扫描失败 - {e} ❌")
            print(f"停止Piezo扫描失败: {e}")

    def send_piezo_command(self, command_num, command_value, description=""):
        """发送Piezo指令的辅助方法"""
        if self.client_thread.socket:
            frame = bytearray()
            frame.append(0xBB)  # 包头
            frame.append(command_num)  # 指令序号
            frame.extend(struct.pack('<I', command_value))  # 指令值 (小端序u32)
            frame.extend(b'\x0D\x0A')  # 包尾

            self.client_thread.socket.send(frame)
            hex_string = ' '.join(f'{byte:02X}' for byte in frame)
            print(f"发送Piezo指令 - {description}: {hex_string}")
            time.sleep(0.05)  # 指令间延时
        else:
            raise Exception("套接字未连接")

    def connect_tcp(self):
        """处理“连接”按钮点击事件，启动TCP连接线程"""
        ip = self.ip_input.text()
        try:
            port = int(self.port_input.text())
        except ValueError:
            self.status_label.setText("状态: 端口号无效 ❌")
            return

        if self.client_thread and self.client_thread.isRunning():
            self.status_label.setText("状态: 已经在连接中... ⚠️")
            return

        self.status_label.setText(f"状态: 正在连接到 {ip}:{port}... 🟡")
        self.connect_button.setEnabled(False)     # 禁用连接按钮
        self.disconnect_button.setEnabled(True)  # 启用断开按钮
        self.parsed_data_browser.clear()         # 清空显示区域

        # 创建并启动TCP客户端工作线程
        self.client_thread = TcpClientWorker(ip, port)
        # 连接线程信号到主窗口的槽函数
        self.client_thread.signals.connected.connect(self.on_connected)
        self.client_thread.signals.disconnected.connect(self.on_disconnected)
        self.client_thread.signals.error.connect(self.on_error)
        self.client_thread.signals.parsed_data.connect(self.update_parsed_data)
        self.client_thread.signals.statistics_updated.connect(self.update_statistics)
        self.client_thread.signals.command_confirmed.connect(self.on_command_confirmed)

        # 创建鲁棒发送线程
        import weakref
        self.sender_thread = RobustSender(weakref.ref(self.client_thread))
        # 连接发送线程信号
        self.sender_thread.signals.command_sent.connect(self.on_command_sent)
        self.sender_thread.signals.command_confirmed.connect(self.on_sender_command_confirmed)
        self.sender_thread.signals.command_timeout.connect(self.on_command_timeout)
        self.sender_thread.signals.send_progress.connect(self.on_send_progress)
        self.sender_thread.signals.send_completed.connect(self.on_send_completed)
        self.sender_thread.signals.send_error.connect(self.on_send_error)

        # 连接接收线程的指令确认信号到发送线程
        self.client_thread.signals.command_confirmed.connect(self.sender_thread.on_command_confirmed)

        # 设置初始的解析参数
        try:
            max_groups = int(self.parse_groups_input.text())
            self.client_thread.set_parse_groups(max_groups)
        except ValueError:
            pass

        self.client_thread.start()

    def disconnect_tcp(self):
        """处理“断开”按钮点击事件，停止TCP连接线程"""
        if self.client_thread:
            self.status_label.setText("状态: 正在断开连接... 🟠")
            self.client_thread.stop() # 调用线程的停止方法
            self.client_thread = None # 清除线程引用
        self.connect_button.setEnabled(True)  # 启用连接按钮
        self.disconnect_button.setEnabled(False) # 禁用断开按钮

    def on_connected(self):
        """TCP连接成功时更新UI状态"""
        self.status_label.setText("状态: 已连接 ✅")
        self.send_button.setEnabled(True)  # 连接成功后启用发送按钮
        self.piezo_init_button.setEnabled(True)  # 启用Piezo初始化按钮

        # 启动波形更新定时器
        if self.waveform_enable_checkbox.isChecked():
            self.waveform_timer.start()

        # 如果数据记录已启用，开始记录
        if self.logging_checkbox.isChecked() and self.selected_log_file:
            import weakref
            success = self.client_thread.enable_data_logging(self.selected_log_file, weakref.ref(self))
            if success:
                selected_count = len(self.get_selected_channels())
                self.log_file_label.setText(f"正在记录: {os.path.basename(self.selected_log_file)} ({selected_count}通道)")
                self.log_file_label.setStyleSheet("color: blue; font-weight: bold;")
            else:
                self.logging_checkbox.setChecked(False)
                self.log_file_label.setText("记录启动失败")
                self.log_file_label.setStyleSheet("color: red;")

    def on_disconnected(self):
        """TCP断开连接时更新UI状态"""
        self.status_label.setText("状态: 已断开连接 🔴")
        self.connect_button.setEnabled(True)
        self.disconnect_button.setEnabled(False)
        self.send_button.setEnabled(False)  # 断开连接后禁用发送按钮

        # 停止波形更新定时器
        self.waveform_timer.stop()

        # 禁用Piezo控制按钮并重置状态
        self.piezo_init_button.setEnabled(False)
        self.piezo_scan_up_button.setEnabled(False)
        self.piezo_scan_down_button.setEnabled(False)
        self.piezo_stop_button.setEnabled(False)
        self.piezo_initialized = False
        self.piezo_status_label.setText("状态: 未初始化")
        self.piezo_status_label.setStyleSheet("color: gray;")

        # 更新数据记录状态显示
        if self.logging_checkbox.isChecked() and self.selected_log_file:
            self.log_file_label.setText(f"准备记录: {os.path.basename(self.selected_log_file)}")
            self.log_file_label.setStyleSheet("color: orange;")

    def on_error(self, message):
        """接收到错误信息时更新UI状态"""
        self.status_label.setText(f"状态: 错误 - {message} ❌")
        # 确保在错误发生时按钮状态正确
        self.connect_button.setEnabled(True)
        self.disconnect_button.setEnabled(False)
        self.send_button.setEnabled(False)  # 错误时禁用发送按钮

        # 禁用Piezo控制按钮并重置状态
        self.piezo_init_button.setEnabled(False)
        self.piezo_scan_up_button.setEnabled(False)
        self.piezo_scan_down_button.setEnabled(False)
        self.piezo_stop_button.setEnabled(False)
        self.piezo_initialized = False
        self.piezo_status_label.setText("状态: 未初始化")
        self.piezo_status_label.setStyleSheet("color: gray;")

        if self.client_thread:
            self.client_thread.stop_client() # 确保线程停止
            self.client_thread = None

    def send_command(self):
        """发送单个指令到下位机（使用鲁棒发送）"""
        if not self.client_thread or not self.client_thread.isRunning():
            self.status_label.setText("状态: 未连接，无法发送指令 ❌")
            return

        try:
            # 获取输入的指令序号和指令值
            command_num = int(self.command_input.text())
            command_value = int(self.value_input.text())

            # 验证输入范围
            # 指令序号是u8，范围0-255
            if not (0 <= command_num <= 255):
                self.status_label.setText("状态: 指令序号超出范围 (0-255) ❌")
                return
            # 指令值是u32，范围0-4294967295
            if not (0 <= command_value <= 0xFFFFFFFF):
                self.status_label.setText("状态: 指令值超出范围 (0-4294967295) ❌")
                return

        except ValueError:
            self.status_label.setText("状态: 请输入有效的数字 ❌")
            return

        # 使用鲁棒发送
        if self.sender_thread:
            # 清空之前的队列，添加新指令
            self.sender_thread.clear_queue()
            self.sender_thread.add_command(command_num, command_value)

            # 如果发送线程没有运行，启动它
            if not self.sender_thread.isRunning():
                self.sender_thread.start()

            self.status_label.setText(f"状态: 🚀 准备发送指令 - 序号:{command_num}, 值:{command_value}")
        else:
            self.status_label.setText("状态: 发送线程未初始化 ❌")

    # --- 通道选择相关方法 ---
    def select_all_channels(self):
        """选择所有通道"""
        for checkbox in self.channel_selections.values():
            checkbox.setChecked(True)

    def select_no_channels(self):
        """取消选择所有通道"""
        for checkbox in self.channel_selections.values():
            checkbox.setChecked(False)

    def select_basic_channels(self):
        """选择基础数据通道"""
        # 先全部取消选择
        self.select_no_channels()
        # 然后选择基础通道
        basic_keys = ['frame_id', 'pwm_count', 'sample_interval']
        for key in basic_keys:
            if key in self.channel_selections:
                self.channel_selections[key].setChecked(True)

    def select_analog_channels(self):
        """选择所有模拟通道"""
        # 先全部取消选择
        self.select_no_channels()
        # 然后选择模拟通道
        analog_keys = [key for key in self.channel_selections.keys()
                      if 'analog' in key or key in ['pwm_count', 'sample_interval']]
        for key in analog_keys:
            if key in self.channel_selections:
                self.channel_selections[key].setChecked(True)

    def get_selected_channels(self):
        """获取当前选中的通道列表"""
        selected = []
        for key, checkbox in self.channel_selections.items():
            if checkbox.isChecked():
                selected.append(key)
        return selected

    def update_waveform_display(self):
        """更新波形显示 - 示波器模式，波形从左到右前进"""
        try:
            if not self.waveform_enable_checkbox.isChecked():
                return

            if not self.client_thread or not self.client_thread.isRunning():
                return

            # 获取当前选择的通道
            selected_channel_name = self.waveform_channel_combo.currentText()

            # 通道名称到键值的映射
            channel_name_to_key = {
                "模拟输出1_uV": "analog_out_1",
                "模拟输出2_uV": "analog_out_2",
                "模拟输出3_uV": "analog_out_3",
                "模拟输出4_uV": "analog_out_4",
                "模拟输入1_V": "analog_in_1_v",
                "模拟输入2_V": "analog_in_2_v",
                "模拟输入3_V": "analog_in_3_v",
                "模拟输入4_V": "analog_in_4_v",
                "模拟输入1_原始值": "analog_in_1_raw",
                "模拟输入2_原始值": "analog_in_2_raw",
                "模拟输入3_原始值": "analog_in_3_raw",
                "模拟输入4_原始值": "analog_in_4_raw",
                "PWM计数值": "pwm_count",
                "采样间隔_us": "sample_interval",
                "帧ID": "frame_id"
            }

            channel_key = channel_name_to_key.get(selected_channel_name)
            if not channel_key:
                return

            # 获取波形数据
            x_data, y_data = self.client_thread.get_waveform_data(channel_key)

            if len(x_data) > 0 and len(y_data) > 0:
                # 过滤掉NaN值，只显示有效数据点
                valid_indices = []
                valid_x = []
                valid_y = []

                for i, (x, y) in enumerate(zip(x_data, y_data)):
                    if not np.isnan(y):  # 只保留非NaN的数据点
                        valid_x.append(x)
                        valid_y.append(y)

                if len(valid_x) > 0:
                    # 更新波形（只显示有效数据点）
                    self.waveform_curve.setData(valid_x, valid_y)

                    # 实现示波器的滚动效果
                    current_pos = self.client_thread.waveform_x_index
                    has_wrapped = self.client_thread.waveform_has_wrapped
                    buffer_size = self.client_thread.waveform_buffer_size

                    # 设置显示窗口大小（类似示波器的屏幕宽度）
                    display_window = 2000  # 显示2000个数据点的窗口，更容易看到效果

                    if has_wrapped:
                        # 已经循环过，实现示波器的循环显示效果
                        # 让显示窗口跟随当前写入位置移动

                        # 计算显示窗口，让当前写入位置在窗口的中间偏右位置
                        window_center = current_pos
                        window_start = max(0, window_center - display_window * 0.7)  # 当前位置在窗口的70%处
                        window_end = window_start + display_window

                        # 处理窗口边界情况
                        if window_end > buffer_size:
                            # 窗口超出缓冲区右边界，调整到缓冲区末尾
                            window_end = buffer_size
                            window_start = max(0, window_end - display_window)
                        elif window_start < 0:
                            # 窗口超出缓冲区左边界，调整到缓冲区开头
                            window_start = 0
                            window_end = min(buffer_size, display_window)

                        self.waveform_widget.setXRange(window_start, window_end, padding=0)
                        status = f"循环模式 - 写入位置: {current_pos} (显示: {int(window_start)}-{int(window_end)})"
                    else:
                        # 还没循环，波形从左到右填充
                        if current_pos > display_window:
                            # 开始滚动，保持窗口大小，让当前位置在窗口右侧
                            window_start = current_pos - display_window + 200  # 当前位置在窗口右侧200点处
                            window_end = current_pos + 200
                            self.waveform_widget.setXRange(window_start, window_end, padding=0)
                            status = f"滚动模式 - 位置: {current_pos} (显示: {int(window_start)}-{int(window_end)})"
                        else:
                            # 显示从0开始，但保持最小窗口大小
                            window_end = max(display_window, current_pos + 200)
                            self.waveform_widget.setXRange(0, window_end, padding=0)
                            status = f"填充模式 - 位置: {current_pos}"

                    self.waveform_widget.setTitle(f"{selected_channel_name} - {status} (有效点数: {len(valid_y)})")

                    # 更新当前写入位置指示线
                    if hasattr(self, 'current_pos_line'):
                        self.current_pos_line.setPos(current_pos)
                else:
                    # 没有有效数据，清空显示
                    self.waveform_curve.setData([], [])
                    self.waveform_widget.setTitle(f"{selected_channel_name} - 无数据")

        except Exception as e:
            print(f"更新波形显示时出错: {e}")



    def update_parsed_data(self, parsed_data):
        """显示解析数据，支持用户自定义显示数量"""
        # 获取用户设置的显示数据组数
        try:
            max_groups_to_display = int(self.display_groups_input.text())
            if max_groups_to_display <= 0:
                max_groups_to_display = 2  # 默认显示2组
        except (ValueError, AttributeError):
            max_groups_to_display = 2  # 默认显示2组

        output_text = f"帧ID: {parsed_data['帧ID']}\n"
        output_text += f"ARM ID: {parsed_data['ARM ID']}\n"
        output_text += f"悬停结果: {parsed_data['悬停结果']}\n"
        output_text += f"悬停计数: {parsed_data['悬停计数值']}\n"
        output_text += f"数据组数: {parsed_data['数据组数n']}"

        # 显示解析信息
        if '已解析数据组数' in parsed_data:
            output_text += f" (已解析: {parsed_data['已解析数据组数']}"
            if parsed_data['跳过数据组数'] > 0:
                output_text += f", 跳过: {parsed_data['跳过数据组数']}"
            output_text += ")"
        output_text += "\n\n"

        # 完整显示指定数量的数据组信息
        groups_to_show = min(max_groups_to_display, len(parsed_data['数据组']))
        for i in range(groups_to_show):
            group = parsed_data['数据组'][i]
            output_text += f"数据组 {i+1}:\n"
            output_text += f"  PWM计数: {group['PWM计数值']}\n"
            output_text += f"  模拟输出1: {group['模拟输出1_uV']} uV\n"
            output_text += f"  模拟输出2: {group['模拟输出2_uV']} uV\n"
            output_text += f"  模拟输出3: {group['模拟输出3_uV']} uV\n"
            output_text += f"  模拟输出4: {group['模拟输出4_uV']} uV\n"
            output_text += f"  采样间隔: {group['采样间隔_us']} us\n"
            output_text += f"  模拟输入1: {group['模拟输入1_V']:.3f}V (原始值: {group['模拟输入1_原始值']})\n"
            output_text += f"  模拟输入2: {group['模拟输入2_V']:.3f}V (原始值: {group['模拟输入2_原始值']})\n"
            output_text += f"  模拟输入3: {group['模拟输入3_V']:.3f}V (原始值: {group['模拟输入3_原始值']})\n"
            output_text += f"  模拟输入4: {group['模拟输入4_V']:.3f}V (原始值: {group['模拟输入4_原始值']})\n"

        # 显示未显示的数据组信息
        parsed_groups = len(parsed_data['数据组'])
        if parsed_groups > max_groups_to_display:
            output_text += f"\n... 还有 {parsed_groups - max_groups_to_display} 组已解析数据未显示 ...\n"

        # 显示跳过的数据组信息
        if '跳过数据组数' in parsed_data and parsed_data['跳过数据组数'] > 0:
            output_text += f"... 跳过解析 {parsed_data['跳过数据组数']} 组数据 ...\n"

        output_text += f"\n包尾: {parsed_data['包尾']}"

        self.parsed_data_browser.setText(output_text)

    def update_statistics(self, stats):
        """
        更新统计信息显示
        Args:
            stats (dict): 统计信息字典
        """
        stats_text = (
            f"帧数: {stats['帧数']:,} | "
            f"数据组: {stats['数据组总数']:,} | "
            f"字节数: {stats['接收字节数']:,} | "
            f"运行时间: {stats['运行时间']} | "
            f"帧率: {stats['帧率']} | "
            f"数据速率: {stats['数据速率']}"
        )
        self.stats_label.setText(stats_text)

    def on_command_confirmed(self, command_num, command_value):
        """
        处理指令确认信号（来自接收线程）
        Args:
            command_num (int): 指令序号
            command_value (int): 指令值
        """
        # 更新状态标签显示指令确认信息
        self.status_label.setText(f"状态: ✅ 指令已确认 - 序号:{command_num}, 值:{command_value}")

    # --- 鲁棒发送相关的信号处理方法 ---
    def on_command_sent(self, command_num, command_value):
        """处理指令发送信号"""
        print(f"GUI: 指令已发送 - 序号:{command_num}, 值:{command_value}")

    def on_sender_command_confirmed(self, command_num, command_value):
        """处理发送线程的指令确认信号"""
        print(f"GUI: 发送线程确认指令 - 序号:{command_num}, 值:{command_value}")

    def on_command_timeout(self, command_num, command_value):
        """处理指令超时信号"""
        self.status_label.setText(f"状态: ⏰ 指令超时 - 序号:{command_num}, 值:{command_value}")
        print(f"GUI: 指令发送超时 - 序号:{command_num}, 值:{command_value}")

    def on_send_progress(self, current, total):
        """处理发送进度信号"""
        self.status_label.setText(f"状态: 📤 发送进度 {current}/{total}")

        # 如果是Piezo初始化过程，更新Piezo状态
        if hasattr(self, 'piezo_status_label') and total > 10:  # 假设Piezo初始化有很多指令
            self.piezo_status_label.setText(f"状态: 正在初始化... ({current}/{total})")

        print(f"GUI: 发送进度 {current}/{total}")

    def on_send_completed(self):
        """处理发送完成信号"""
        self.status_label.setText("状态: ✅ 批量发送完成")
        print("GUI: 批量发送完成")

    def on_send_error(self, error_msg):
        """处理发送错误信号"""
        self.status_label.setText(f"状态: ❌ 发送错误 - {error_msg}")
        print(f"GUI: 发送错误 - {error_msg}")

    def on_piezo_init_completed(self):
        """处理Piezo初始化完成"""
        self.piezo_initialized = True
        self.piezo_status_label.setText("状态: 初始化完成 ✅")
        self.piezo_status_label.setStyleSheet("color: green;")
        # 启用扫描按钮
        self.piezo_scan_up_button.setEnabled(True)
        self.piezo_scan_down_button.setEnabled(True)
        self.piezo_stop_button.setEnabled(True)
        print("Piezo初始化完成，所有配置参数发送成功")

        # 断开信号连接，避免重复触发
        try:
            self.sender_thread.signals.send_completed.disconnect(self.on_piezo_init_completed)
        except:
            pass


# --- 程序入口 ---
if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
