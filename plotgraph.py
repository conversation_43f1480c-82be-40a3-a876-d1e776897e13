import numpy as np
import pyqtgraph as pg
from PyQt6.QtCore import Qt, pyqtSlot
from PyQt6.QtWidgets import QWidget, QVBoxLayout

class LogGPlot(QWidget):
    def __init__(self, window_size=10000, update_size=20, parent=None):
        """
        初始化 Log(G/G0) 实时绘图类
        
        参数:
            window_size: 视图窗口显示的数据点数量
            update_size: 每次更新的数据点数量
            parent: 父窗口
        """
        super().__init__(parent)
        self.window_size = window_size
        self.update_size = update_size
        
        # 初始化数据缓存，填充 NaN 表示无数据
        self.data = np.empty(self.window_size)
        self.data[:] = np.nan  # 用 NaN 填充初始数据
        self.ptr = 0  # 数据写入指针，表示当前写入的数据点总数
        
        self.piezo_data = np.empty(self.window_size) # 新增Piezo数据缓存
        self.piezo_data[:] = np.nan
        # 设置布局
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建绘图小部件
        self.setup_plot_widget()
        
    def setup_plot_widget(self):
        """设置绘图小部件"""
        # 创建绘图窗口
        self.plot_widget = pg.PlotWidget()
        self.layout.addWidget(self.plot_widget)
        
        # 设置绘图样式
        self.plot_widget.setBackground('w')  # 白色背景
        self.plot_widget.showGrid(x=True, y=True, alpha=0.3)
        
        # 设置轴标签
        self.plot_widget.setLabel('left', 'Log(G/G0) & Piezo Voltage (V)')
        self.plot_widget.setLabel('bottom', 'Sample Index')
        
        # 设置固定的Y轴范围 (5 到 -10)
        self.plot_widget.setYRange(-10, 12, padding=0)
        
        
        # 添加曲线
        self.curve = self.plot_widget.plot(pen=pg.mkPen('b', width=1.5))
        self.piezo_curve = self.plot_widget.plot(pen=pg.mkPen('r', width=1.5), name='Piezo Voltage') # 新增Piezo曲线，红色

        # 禁用鼠标缩放和拖动，保持窗口固定
        self.plot_widget.setMouseEnabled(x=False, y=False)
        
        # 设置 X 轴范围的自动模式为 False，我们自己管理
        self.plot_widget.getPlotItem().vb.setAutoVisible(y=False)
        self.plot_widget.getPlotItem().vb.setAutoVisible(x=False)
        
        # 初始设置视图窗口的 X 轴范围
        self.plot_widget.setXRange(0, self.window_size)
        
        # 初始曲线数据为空
        self.curve.setData(np.arange(self.window_size), self.data)
        self.piezo_curve.setData(np.arange(self.window_size), self.piezo_data)

    @pyqtSlot(list)
    def update_data(self, new_data, new_piezo_data):
        """
        更新数据缓存并更新图表
        
        参数:
            new_data: 包含Log(G/G0)值的列表或数组
        """
        if not isinstance(new_data, (list, np.ndarray)):
            print("错误：输入数据必须是列表或NumPy数组")
            return
        
        # 处理为列表的情况
        if isinstance(new_data, list):
            new_data = np.array(new_data)
        if isinstance(new_piezo_data, list):
            new_piezo_data = np.array(new_piezo_data)
        # 确保数据长度不超过更新大小
        n_new_points = min(len(new_data), len(new_piezo_data), self.update_size)
        if n_new_points == 0:
            return
        
        # 关键：数据向左移动，新数据从右侧填充
        # 将旧数据向左移动 n_new_points 个位置
        self.data[:-n_new_points] = self.data[n_new_points:]
        self.piezo_data[:-n_new_points] = self.piezo_data[n_new_points:]

        # 将新数据添加到数组的末尾
        self.data[-n_new_points:] = new_data[:n_new_points]
        self.piezo_data[-n_new_points:] = new_piezo_data[:n_new_points]
        # 更新数据指针
        self.ptr += n_new_points
        
        # 更新曲线数据，始终绘制缓存区中的数据
        # X 坐标始终是 0 到 window_size - 1
        self.curve.setData(np.arange(self.window_size), self.data)
        self.piezo_curve.setData(np.arange(self.window_size), self.piezo_data)
        
        # 更新 X 轴标签显示当前样本索引范围
        if self.ptr < self.window_size:
            current_x_label = f'Sample Index (0 - {self.ptr - 1})'
        else:
            current_x_label = f'Sample Index ({self.ptr - self.window_size} - {self.ptr - 1})'
        self.plot_widget.setLabel('bottom', current_x_label)
    
    def clear_data(self):
        """清除所有数据"""
        self.data[:] = np.nan  # 用 NaN 填充
        self.piezo_data[:] = np.nan # 清除Piezo数据
        self.ptr = 0
        self.curve.setData(np.arange(self.window_size), self.data)
        self.piezo_curve.setData(np.arange(self.window_size), self.piezo_data)
        self.plot_widget.setLabel('bottom', 'Sample Index')
    
    def set_window_size(self, new_size):
        """
        设置新的窗口大小
        
        参数:
            new_size: 新的窗口大小
        """
        if new_size <= 0:
            print("错误：窗口大小必须大于0")
            return
        
        # 创建新的数据缓存
        new_data = np.empty(new_size)
        new_data[:] = np.nan  # 用 NaN 填充
        new_piezo_data = np.empty(new_size)
        new_piezo_data[:] = np.nan
        # 如果新窗口大小小于当前数据量，则只取最新的数据
        if self.ptr > 0:
            if new_size >= self.ptr:
                # 新窗口可以容纳所有数据
                copy_size = min(self.window_size, self.ptr)
                copy_size_piezo = min(self.window_size, self.ptr)
                if self.ptr <= self.window_size:
                    # 当前数据没有填满缓存区
                    new_data[new_size - self.ptr:] = self.data[:self.ptr]
                    new_piezo_data[new_size - self.ptr:] = self.piezo_data[:self.ptr]
                else:
                    # 当前数据已经填满缓存区，只能保留最新的window_size个数据
                    new_data[new_size - copy_size:] = self.data[self.window_size - copy_size:]
                    new_piezo_data[new_size - copy_size_piezo:] = self.piezo_data[self.window_size - copy_size_piezo:]
            else:
                # 新窗口不能容纳所有数据，只取最新的数据
                if self.ptr <= self.window_size:
                    # 当前数据没有填满缓存区
                    start_idx = max(0, self.ptr - new_size)
                    new_data = self.data[start_idx:self.ptr]
                    new_piezo_data = self.piezo_data[start_idx:self.ptr]
                else:
                    # 当前数据已经填满缓存区，只能保留最新的new_size个数据
                    new_data = self.data[self.window_size - new_size:]
                    new_piezo_data = self.piezo_data[self.window_size - new_size:]
        
        # 更新数据缓存和窗口大小
        self.data = new_data
        self.piezo_data = new_piezo_data
        self.window_size = new_size
        
        # 更新 X 轴范围
        self.plot_widget.setXRange(0, self.window_size)
        
        # 更新曲线数据
        self.curve.setData(np.arange(self.window_size), self.data)
        self.piezo_curve.setData(np.arange(self.window_size), self.piezo_data)
        # 更新 X 轴标签
        if self.ptr < self.window_size:
            current_x_label = f'Sample Index (0 - {self.ptr - 1})'
        else:
            current_x_label = f'Sample Index ({self.ptr - self.window_size} - {self.ptr - 1})'
        self.plot_widget.setLabel('bottom', current_x_label) 