# mode_state_machine.py
import HDControll # 导入 HDControll 模块来使用其定义的功能和模式枚举
from enum import Enum
import time
from settings import settings
from Status import Status, MotorMode, MotorFlowStep, HoverSate, HoverStep

class ModeStateMachine():
    """
    上位机主模式状态机
    管理 Manual, STM_01_PIEZO, STM_02_MOTOR, STM_03_MOTOR_PIEZO 四种模式
    """

    def __init__(self, status: Status):
        super().__init__()
        # 接收外部传入的Status实例
        self.status = status
        print(f"模式状态机初始化，当前模式: {self.status.flow.Mode.value}")
        print(f"模式状态机初始化，当前状态: {self.status.flow.State.value}")

    @property
    def current_mode(self) -> MotorMode:
        """获取当前的模式"""
        return self.status.flow.Mode

    @current_mode.setter
    def current_mode(self, new_mode: MotorMode):
        """设置当前的模式"""
        if not isinstance(new_mode, MotorMode):
            raise TypeError("current_mode 必须是 MotorMode 枚举类型")
        self.status.flow.Mode = new_mode # 赋值给Status中的flow数据

    @property
    def current_modestate(self) -> MotorFlowStep:
        """获取当前的状态"""
        return self.status.flow.State
    
    def set_mode(self, new_mode: MotorMode):
        """
        设置新的主模式并执行相应的初始化逻辑
        参数:
            new_mode: HDControll.MainMode 枚举值，新的模式
        """
        if new_mode == self.status.flow.Mode:
            print('无需切换')
            return

        print(f"进入模式: {new_mode.value}...")
        self.current_mode = new_mode
        # 原程序中无模式的退出处理 所以只保留模式进入的程序
        self._enter_mode(self.status.flow.Mode)
  

    def _enter_mode(self,new_mode:MotorMode):
        # 初始化的部分 对应 macro start 
        print(f'{new_mode.value}模式进入初始化')
        HDControll.setAOMode('RE',HDControll.AOMode.Constant) 
        HDControll.setAOMode('Bias',HDControll.AOMode.Constant) 
        HDControll.AMP_State('Logarithmic')        
        # 这里只在退出01或03的时候触发
        HDControll.setAOMode('Piezo',HDControll.AOMode.Zero)      
        # 原逻辑中在退出02的时候才会触发emm 
        HDControll.Motor_Stop()
        if new_mode == MotorMode.MANUAL:
            return
        else:
            if self.status.flow.State in (MotorFlowStep.LOGG_UPWARD, MotorFlowStep.LOGG_EXCEED_UPPER):
                self._set_mode_state(MotorFlowStep.LOGG_UPWARD)
            else:
                # TODO：困惑 这个地方在labview中是会因为error又跳回upward..不知道这样做的意义？
                # 先改成UPWARD
                self._set_mode_state(MotorFlowStep.LOGG_UPWARD)
        
    def _set_mode_state(self, new_modestate:MotorFlowStep):
        """
        设置核心的流步骤，并执行相应的进入/退出逻辑。
        这是驱动状态机的主要方法。
        """
        print(f'进入状态{new_modestate}')
        if new_modestate == self.status.flow.State:
            # print(f'无需切换状态{new_modestate.value}')
            return
        # print(f'切换状态至{new_modestate}')
        self.status.flow.State = new_modestate
        # 搞错了 状态判断放在事件中修改就行 不需要单独的进入处理 TODO:反转模式还没实现


    def process_data(self):
        """
        状态机的主驱动函数。
        根据当前的 MotorFlowStep 和 OperationMode 处理controller中的数据。
        这个函数会在每次数据更新时被调用。
        """
        # 检查是否有数据
        if not self.status.controller.data_points:
            return

        # 获取最新的数据点
        latest_data_point = self.status.controller.get_latest_data_point()
        piezo_value = latest_data_point.Piezo / 1000000  # 转换为伏特
        logg_value = latest_data_point.LOGG
        motor_step = latest_data_point.MotorPosition_Step

        # print(f'接收到电导{logg_value},接受到压电{piezo_value},当前状态{self.status.flow.State},当前模式{self.status.flow.Mode}')
        # 获取电导阈值 (使用字典访问，以避免点语法无法访问带括号的属性) 这里对应labview的newstat的vi
        try:
            # 这两个是物理值 不需要换算
            logg_upper_limit = settings.data['Flow']['Scan']['Log(G/G0)_positive']
            logg_lower_limit = settings.data['Flow']['Scan']['Log(G/G0)_negative']
            # 这两个需要(因为是从控制器读取来的)
            piezo_upper_limit = settings.data['Controller']['Piezo']['ScanUpper']['value'] / 1000000
            piezo_lower_limit = settings.data['Controller']['Piezo']['ScanLower']['value'] / 1000000
            # 这里获取时间段的长度 需要考虑和python的区别
            self.status.flow.StepInPos = (time.perf_counter() * 1000 - int(self.status.flow.Motor_Step_Tick)) >= 100
            # 注意：这里不再直接设置controller的值，因为controller现在是数组
            # print(f'{self.status.flow.StepInPos}and{(time.perf_counter() * 1000  - int(self.status.flow.Motor_Step_Tick))}')
        except KeyError as e:
            print(f"警告: 无法获取电导阈值，请检查settings.json: {e}")
            return # 如果没有阈值，无法进行判断
        # 注意了！！！ LoggDownward 是让电机上升！！！   TODO:暂无反转判断
        # 瞬时状态 更改玩方向就转移
        # UPWARD
        if self.status.flow.State == MotorFlowStep.LOGG_UPWARD:
            # UPWARD - 01
            if self.status.flow.Mode == MotorMode.STM_01_PIEZO:
                print('针尖开始下降')
                HDControll.setAOMode('Piezo',HDControll.AOMode.SingleScan)      
                HDControll.SingleScan_Up('Piezo')                
            # UPWARD - 02
            elif self.status.flow.Mode == MotorMode.STM_02_MOTOR:
                print('电机开始下降')
                HDControll.Motor_Downward()
            # UPWARD - 03
            elif self.status.flow.Mode == MotorMode.STM_03_MOTOR_PIEZO:
                # EXCEEDUPPER
                if piezo_value >= piezo_upper_limit:
                    self.status.flow.StepInPos = False
                    # 记录步数
                    self.Motor_Step('DOWN')
                    self.status.flow.Motor_Step_Tick = time.perf_counter() * 1000
                    self.status.flow.Piezo_EU_DOff = piezo_lower_limit + 1 <= piezo_value
                else:
                    print('针尖开始下降')
                    HDControll.setAOMode('Piezo',HDControll.AOMode.SingleScan)      
                    HDControll.SingleScan_Up('Piezo')                
                # 检查单位换算
                pass
            self._set_mode_state(MotorFlowStep.LOGG_EXCEED_UPPER)
        # DOWNWARD
        elif self.status.flow.State == MotorFlowStep.LOGG_DOWNWARD:
            # DOWNWARD - 01
            if self.status.flow.Mode == MotorMode.STM_01_PIEZO:
                print('针尖开始上升')
                HDControll.setAOMode('Piezo',HDControll.AOMode.SingleScan)
                HDControll.SingleScan_Down('Piezo')
            # DOWNWARD - 02
            elif self.status.flow.Mode == MotorMode.STM_02_MOTOR:
                print('电机开始上升')
                HDControll.Motor_Upward()
            # DOWNWARD - 03
            elif self.status.flow.Mode == MotorMode.STM_03_MOTOR_PIEZO:
                # EXCEEDLOWER
                if piezo_value <= piezo_lower_limit:
                    self.status.flow.StepInPos = False
                    # 记录steptick 并 position记录往上一步
                    self.status.flow.Motor_Step_Tick = time.perf_counter()* 1000
                    self.Motor_Step('UP')
                else:
                    print('针尖开始上升')
                    HDControll.setAOMode('Piezo',HDControll.AOMode.SingleScan)      
                    HDControll.SingleScan_Down('Piezo')                    
            self._set_mode_state(MotorFlowStep.LOGG_EXCEED_LOWER)
            # 记录loggcmdtick初始时间
        # EXCEEDLOWER
        elif self.status.flow.State == MotorFlowStep.LOGG_EXCEED_LOWER:
            if self.status.flow.Hover.Busy:
                self.Hover_Action()
            else:
                # 低电导
                if logg_value <= logg_lower_limit:
                    self._set_mode_state(MotorFlowStep.LOGG_UPWARD)
                else:
                    if self.status.flow.Mode == MotorMode.STM_03_MOTOR_PIEZO:
                        # 当电机到位且低电导 暂无mcbj 如果mcbj这里需要判断 将lower改成upper
                        if self.status.flow.StepInPos and piezo_value <= piezo_lower_limit:
                            self._set_mode_state(MotorFlowStep.LOGG_DOWNWARD)
                        else:
                            self._set_mode_state(MotorFlowStep.LOGG_EXCEED_LOWER)
                    pass
        # EXCEEDUPPER
        elif self.status.flow.State == MotorFlowStep.LOGG_EXCEED_UPPER:
            # 高电导
            if logg_value >= logg_upper_limit:
                self._set_mode_state(MotorFlowStep.LOGG_DOWNWARD)
            else:
                if self.status.flow.Mode == MotorMode.STM_03_MOTOR_PIEZO:
                    # 当电机到位且高电导
                    # Motor_Step_In_Pos 目前应为还是false 会导致直接跳转 需要在labview中找到设置逻辑
                    if self.status.flow.StepInPos and piezo_value >= piezo_upper_limit:
                        self._set_mode_state(MotorFlowStep.LOGG_UPWARD)
                    else:
                        self._set_mode_state(MotorFlowStep.LOGG_EXCEED_UPPER)
                pass
        pass

    def cmd_monitor(self):
        pass

    def Motor_Step(self, Dir):
        print('电机当前位置:',self.status.controller.MotorPosition_Step)
        # 这里有点奇怪
        if Dir == 'UP':
            self.status.controller.MotorPosition_Step -= 1
        else:
            self.status.controller.MotorPosition_Step += 1
        pass
        HDControll.Motor_StepValue(self.status.controller.MotorPosition_Step)
        HDControll.Motor_Step()
        HDControll.Motor_Step()

        
    def Hover_Action(self):
        """悬停动作处理"""
        # TODO: 实现悬停逻辑
        pass
    # TODO:错误处理 cmd vi 由于不直接起作用 放到后面实现
    # TODO:悬停逻辑
    # TODO:CMD Monitor 对异常情况进行处理
    # TODO:stm03状态 step似乎无法正常控制电机 - 可能是占空比设置的问题，在测试
    # TODO:怀疑有bug 当设置的时候会调用检查controller的区别的vi但在设置上下限的时候也是（上下限不在controller里面）请检查